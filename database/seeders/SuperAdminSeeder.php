<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create super admin user
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'phone' => '01234567890', // Default admin phone
                'password' => Hash::make('RoyalTransit2024!'),
                'email_verified_at' => now(),
                'user_type_id' => 1, // Admin user type
            ]
        );

        // Ensure super_admin role exists
        $superAdminRole = Role::firstOrCreate(['name' => 'super_admin']);

        // Get all permissions
        $permissions = Permission::all();

        // Assign all permissions to super_admin role
        $superAdminRole->syncPermissions($permissions);

        // Assign super_admin role to the user
        $superAdmin->assignRole('super_admin');

        $this->command->info('Super Admin user created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: RoyalTransit2024!');
        $this->command->info('Please change the password after first login.');
    }
}
