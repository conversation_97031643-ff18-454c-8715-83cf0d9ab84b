<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreClientRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }
    
    public function attributes(): array
    {
        return [
            'father_job'           => __('messages.father_job'),
            'mother_job'           => __('messages.mother_job'),
            'mother_phone'         => __('messages.mother_phone'),
            'father_phone'         => __('messages.father_phone'),
            'home_phone'           => __('messages.home_phone'),
            'extra_phone'          => __('messages.extra_phone'),
            'contact_priority'     => __('messages.contact_priority'),
            'address'              => __('messages.address'),
            'area'                 => __('messages.area'),
            'location'             => __('messages.location'),
            'car_type'             => __('messages.car_type'),
            'study_start_date'     => __('messages.study_start_date'),
            'subscription_type'    => __('messages.subscription_type'),
            'client_type'          => __('messages.client_type'),
            'comments'             => __('messages.comments'),
            'students.*.name'      => __('messages.student_name'),
            'students.*.school'    => __('messages.school'),
            'students.*.other_school' => __('messages.other_school'),
            'students.*.education_department' => __('messages.education_department'),
            'students.*.education_stage' => __('messages.education_stage'),
            'students.*.class_level' => __('messages.class_level'),
            'students.*.entry_time' => __('messages.entry_time'),
            'students.*.exit_time'  => __('messages.exit_time'),
        ];
    }

    public function rules(): array
    {
        return [
            'father_job'           => 'required|string',
            'mother_job'           => 'required|string',
            'mother_phone'         => 'required|numeric|regex:/^[0-9]{11}$/',
            'father_phone'         => 'required|numeric|regex:/^[0-9]{11}$/',
            'home_phone'           => 'nullable|numeric',
            'extra_phone'          => 'nullable|numeric',
            'contact_priority'     => 'required|string',
            'address'              => 'required|string',
            'area'                 => 'required|string',
            'location'             => 'nullable|regex:/^-?\d{1,2}\.\d+,\s*-?\d{1,3}\.\d+$/',
            'car_type'             => 'required|string',
            'study_start_date'     => 'required|date',
            'subscription_type'    => 'required|in:one_time,monthly',
            'client_type'          => 'required|in:new,old',
            'comments'             => 'nullable|string',
            // Students validation
            'students'             => 'required|array|min:1',
            'students.*.name'      => 'required|string|max:255',
            'students.*.school'    => 'required|in:' . implode(',', array_keys(__('schools'))),
            'students.*.other_school' => 'nullable|string|max:255',
            'students.*.education_department' => 'required|string|in:national,international,igsec,french,american,dutch',
            'students.*.education_stage' => 'required|string|in:junior,kg,primary,preparatory,secondary',
            'students.*.class_level' => 'required|string|max:50',
            'students.*.entry_time' => 'required|date_format:H:i',
            'students.*.exit_time'  => 'required|date_format:H:i',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $students = $this->input('students', []);

            foreach ($students as $index => $student) {
                if (isset($student['school']) && $student['school'] === 'other') {
                    if (empty($student['other_school'])) {
                        $validator->errors()->add("students.{$index}.other_school", __('validation.required'));
                    }
                }
            }
        });
    }
}
