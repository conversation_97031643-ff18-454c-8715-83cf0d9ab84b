<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Driver extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'phone',
        'email',
        'license_number',
        'license_expiry',
        'national_id',
        'address',
        'hire_date',
        'salary',
        'status',
        'vehicle_type',
        'vehicle_model',
        'vehicle_year',
        'vehicle_plate_number',
        'vehicle_color',
        'vehicle_capacity',
        'vehicle_notes',
        'emergency_contact_name',
        'emergency_contact_phone',
        'emergency_contact_relation',
        'notes'
    ];

    protected $casts = [
        'license_expiry' => 'date',
        'hire_date' => 'date',
        'salary' => 'decimal:2'
    ];

    /**
     * Get the routes assigned to this driver
     */
    public function routes()
    {
        return $this->belongsToMany(TransportRoute::class, 'driver_route', 'driver_id', 'route_id')
                    ->withPivot(['assigned_date', 'end_date', 'status', 'notes'])
                    ->withTimestamps();
    }

    /**
     * Get active routes for this driver
     */
    public function activeRoutes()
    {
        return $this->routes()->wherePivot('status', 'active');
    }

    /**
     * Get the current active route for this driver
     */
    public function currentRoute()
    {
        return $this->activeRoutes()->first();
    }

    /**
     * Check if driver has an active route
     */
    public function hasActiveRoute()
    {
        return $this->activeRoutes()->exists();
    }

    /**
     * Get status badge class for UI
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'active' => 'bg-success',
            'inactive' => 'bg-secondary',
            'suspended' => 'bg-danger'
        ];

        return $badges[$this->status] ?? 'bg-secondary';
    }

    /**
     * Get vehicle info as formatted string
     */
    public function getVehicleInfoAttribute()
    {
        $info = [];
        
        if ($this->vehicle_type) {
            $info[] = ucfirst($this->vehicle_type);
        }
        
        if ($this->vehicle_model && $this->vehicle_year) {
            $info[] = $this->vehicle_model . ' (' . $this->vehicle_year . ')';
        } elseif ($this->vehicle_model) {
            $info[] = $this->vehicle_model;
        }
        
        if ($this->vehicle_plate_number) {
            $info[] = 'Plate: ' . $this->vehicle_plate_number;
        }
        
        return implode(' - ', $info) ?: 'No vehicle info';
    }

    /**
     * Check if license is expiring soon (within 30 days)
     */
    public function isLicenseExpiringSoon()
    {
        return $this->license_expiry && $this->license_expiry->diffInDays(now()) <= 30;
    }

    /**
     * Check if license is expired
     */
    public function isLicenseExpired()
    {
        return $this->license_expiry && $this->license_expiry->isPast();
    }

    /**
     * Scope for active drivers
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for drivers with expiring licenses
     */
    public function scopeWithExpiringLicense($query, $days = 30)
    {
        return $query->where('license_expiry', '<=', now()->addDays($days));
    }
}
