@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        @include('admin.partials.sidebar')

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-user-tie me-2"></i>Driver Details: {{ $driver->name }}
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ route('admin.drivers.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Drivers
                    </a>
                    @can('update admin_drivers')
                    <div class="btn-group me-2">
                        <a href="{{ route('admin.drivers.edit', $driver) }}" class="btn btn-main">
                            <i class="fas fa-edit me-1"></i>Edit Driver
                        </a>
                        <button type="button" class="btn btn-main dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                            <span class="visually-hidden">Toggle Dropdown</span>
                        </button>
                        <ul class="dropdown-menu">
                            @if($driver->status === 'active')
                                <li><a class="dropdown-item" href="#" onclick="suspendDriver({{ $driver->id }})">
                                    <i class="fas fa-ban me-2"></i>Suspend Driver
                                </a></li>
                            @elseif($driver->status === 'suspended')
                                <li><a class="dropdown-item" href="#" onclick="activateDriver({{ $driver->id }})">
                                    <i class="fas fa-check me-2"></i>Activate Driver
                                </a></li>
                            @endif
                            @if($driver->hasActiveRoute())
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-warning" href="#" onclick="removeFromRoute({{ $driver->id }})">
                                    <i class="fas fa-route me-2"></i>Remove from Route
                                </a></li>
                            @endif
                        </ul>
                    </div>
                    @endcan
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- License Expiry Alert -->
            @if($driver->isLicenseExpired())
                <div class="alert alert-danger" role="alert">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>License Expired!</h6>
                    <p class="mb-0">This driver's license expired on {{ $driver->license_expiry->format('F j, Y') }}. Please update the license information immediately.</p>
                </div>
            @elseif($driver->isLicenseExpiringSoon())
                <div class="alert alert-warning" role="alert">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>License Expiring Soon!</h6>
                    <p class="mb-0">This driver's license will expire on {{ $driver->license_expiry->format('F j, Y') }}. Please renew the license soon.</p>
                </div>
            @endif

            <div class="row">
                <!-- Personal Information -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>Personal Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Full Name:</label>
                                        <p class="mb-0">{{ $driver->name }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Status:</label>
                                        <p class="mb-0">
                                            <span class="badge {{ $driver->status_badge }}">
                                                {{ ucfirst($driver->status) }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Phone Number:</label>
                                        <p class="mb-0">{{ $driver->phone }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Email:</label>
                                        <p class="mb-0">{{ $driver->email ?: 'Not provided' }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">National ID:</label>
                                        <p class="mb-0">{{ $driver->national_id }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Hire Date:</label>
                                        <p class="mb-0">{{ $driver->hire_date->format('F j, Y') }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            @if($driver->address)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Address:</label>
                                <p class="mb-0">{{ $driver->address }}</p>
                            </div>
                            @endif
                            
                            @if($driver->salary)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Monthly Salary:</label>
                                <p class="mb-0">SAR {{ number_format($driver->salary, 2) }}</p>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- License Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-id-card me-2"></i>License Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">License Number:</label>
                                        <p class="mb-0">{{ $driver->license_number }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">License Expiry:</label>
                                        <p class="mb-0">
                                            {{ $driver->license_expiry->format('F j, Y') }}
                                            @if($driver->isLicenseExpired())
                                                <span class="badge bg-danger ms-2">EXPIRED</span>
                                            @elseif($driver->isLicenseExpiringSoon())
                                                <span class="badge bg-warning ms-2">EXPIRING SOON</span>
                                            @else
                                                <span class="badge bg-success ms-2">VALID</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Vehicle Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-car me-2"></i>Vehicle Information
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($driver->vehicle_type || $driver->vehicle_model || $driver->vehicle_plate_number)
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Vehicle Type:</label>
                                        <p class="mb-0">{{ $driver->vehicle_type ? ucfirst($driver->vehicle_type) : 'Not specified' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Vehicle Capacity:</label>
                                        <p class="mb-0">{{ $driver->vehicle_capacity ? $driver->vehicle_capacity . ' passengers' : 'Not specified' }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Vehicle Model:</label>
                                        <p class="mb-0">{{ $driver->vehicle_model ?: 'Not specified' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Vehicle Year:</label>
                                        <p class="mb-0">{{ $driver->vehicle_year ?: 'Not specified' }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Plate Number:</label>
                                        <p class="mb-0">{{ $driver->vehicle_plate_number ?: 'Not specified' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Vehicle Color:</label>
                                        <p class="mb-0">{{ $driver->vehicle_color ?: 'Not specified' }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            @if($driver->vehicle_notes)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Vehicle Notes:</label>
                                <p class="mb-0">{{ $driver->vehicle_notes }}</p>
                            </div>
                            @endif
                            @else
                            <div class="text-center text-muted">
                                <i class="fas fa-car fa-3x mb-3"></i>
                                <p>No vehicle information provided</p>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Emergency Contact -->
                    @if($driver->emergency_contact_name || $driver->emergency_contact_phone)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-phone me-2"></i>Emergency Contact
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Contact Name:</label>
                                        <p class="mb-0">{{ $driver->emergency_contact_name ?: 'Not provided' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Contact Phone:</label>
                                        <p class="mb-0">{{ $driver->emergency_contact_phone ?: 'Not provided' }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            @if($driver->emergency_contact_relation)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Relationship:</label>
                                <p class="mb-0">{{ $driver->emergency_contact_relation }}</p>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endif

                    <!-- Route Assignments -->
                    @if($driver->routes->count() > 0)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-route me-2"></i>Route Assignment History
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Route Name</th>
                                            <th>Assigned Date</th>
                                            <th>End Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($driver->routes as $route)
                                        <tr>
                                            <td>{{ $route->name }}</td>
                                            <td>{{ $route->pivot->assigned_date ? \Carbon\Carbon::parse($route->pivot->assigned_date)->format('M j, Y') : 'N/A' }}</td>
                                            <td>{{ $route->pivot->end_date ? \Carbon\Carbon::parse($route->pivot->end_date)->format('M j, Y') : 'Current' }}</td>
                                            <td>
                                                <span class="badge {{ $route->pivot->status === 'active' ? 'bg-success' : 'bg-secondary' }}">
                                                    {{ ucfirst($route->pivot->status) }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ route('admin.routes.show', $route) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if($route->pivot->status === 'active')
                                                    @can('update admin_drivers')
                                                    <button class="btn btn-sm btn-outline-danger ms-1" onclick="removeFromSpecificRoute({{ $driver->id }}, {{ $route->id }})">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                    @endcan
                                                @endif
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Additional Notes -->
                    @if($driver->notes)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-sticky-note me-2"></i>Additional Notes
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">{{ $driver->notes }}</p>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Statistics & Quick Actions -->
                <div class="col-md-4">
                    <!-- Current Assignment -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-route me-2"></i>Current Assignment
                            </h5>
                        </div>
                        <div class="card-body">
                            @php $currentRoute = $driver->currentRoute(); @endphp
                            @if($currentRoute)
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Route:</label>
                                    <p class="mb-0">{{ $currentRoute->name }}</p>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label fw-bold">From - To:</label>
                                    <p class="mb-0">{{ $currentRoute->from_area }} → {{ $currentRoute->to_school }}</p>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Schedule:</label>
                                    <p class="mb-0">
                                        {{ $currentRoute->pickup_time ? $currentRoute->pickup_time->format('h:i A') : 'N/A' }} - 
                                        {{ $currentRoute->dropoff_time ? $currentRoute->dropoff_time->format('h:i A') : 'N/A' }}
                                    </p>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Students:</label>
                                    <p class="mb-0">{{ $currentRoute->current_students }} / {{ $currentRoute->capacity }}</p>
                                </div>
                                
                                <div class="d-grid">
                                    <a href="{{ route('admin.routes.show', $currentRoute) }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View Route Details
                                    </a>
                                </div>
                            @else
                                <div class="text-center text-muted">
                                    <i class="fas fa-route fa-3x mb-3"></i>
                                    <p>No active route assignment</p>
                                    @can('update admin_drivers')
                                    <div class="d-grid">
                                        <a href="{{ route('admin.drivers.edit', $driver) }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-plus me-1"></i>Assign to Route
                                        </a>
                                    </div>
                                    @endcan
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Driver Statistics -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>Driver Statistics
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Total Routes Assigned:</label>
                                <p class="mb-0">{{ $driver->routes->count() }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Active Routes:</label>
                                <p class="mb-0">{{ $driver->activeRoutes->count() }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Years of Service:</label>
                                <p class="mb-0">{{ $driver->hire_date->diffInYears(now()) }} years</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">License Status:</label>
                                <p class="mb-0">
                                    @if($driver->isLicenseExpired())
                                        <span class="badge bg-danger">EXPIRED</span>
                                    @elseif($driver->isLicenseExpiringSoon())
                                        <span class="badge bg-warning">EXPIRING SOON</span>
                                    @else
                                        <span class="badge bg-success">VALID</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    @can('update admin_drivers')
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('admin.drivers.edit', $driver) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit me-1"></i>Edit Driver
                                </a>
                                
                                @if($driver->status === 'active')
                                    <button class="btn btn-outline-warning btn-sm" onclick="suspendDriver({{ $driver->id }})">
                                        <i class="fas fa-ban me-1"></i>Suspend Driver
                                    </button>
                                @elseif($driver->status === 'suspended')
                                    <button class="btn btn-outline-success btn-sm" onclick="activateDriver({{ $driver->id }})">
                                        <i class="fas fa-check me-1"></i>Activate Driver
                                    </button>
                                @endif
                                
                                @if($driver->hasActiveRoute())
                                    <button class="btn btn-outline-danger btn-sm" onclick="removeFromRoute({{ $driver->id }})">
                                        <i class="fas fa-route me-1"></i>Remove from Route
                                    </button>
                                @endif
                                
                                @can('delete admin_drivers')
                                @if(!$driver->hasActiveRoute())
                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteDriver({{ $driver->id }})">
                                        <i class="fas fa-trash me-1"></i>Delete Driver
                                    </button>
                                @endif
                                @endcan
                            </div>
                        </div>
                    </div>
                    @endcan
                </div>
            </div>
        </main>
    </div>
</div>

@push('scripts')
<script>
function suspendDriver(driverId) {
    if (confirm('Are you sure you want to suspend this driver? This will remove them from any active routes.')) {
        fetch(`/admin/drivers/${driverId}/suspend`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error suspending driver');
            }
        });
    }
}

function activateDriver(driverId) {
    if (confirm('Are you sure you want to activate this driver?')) {
        fetch(`/admin/drivers/${driverId}/activate`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error activating driver');
            }
        });
    }
}

function removeFromRoute(driverId) {
    if (confirm('Are you sure you want to remove this driver from their current route?')) {
        // This would need to be implemented in the controller
        alert('Feature coming soon!');
    }
}

function removeFromSpecificRoute(driverId, routeId) {
    if (confirm('Are you sure you want to remove this driver from this route?')) {
        fetch(`/admin/drivers/${driverId}/routes/${routeId}/remove`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error removing driver from route');
            }
        });
    }
}

function deleteDriver(driverId) {
    if (confirm('Are you sure you want to delete this driver? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/drivers/${driverId}`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection

@push('styles')
<style>
    .sidebar-custom {
        background: linear-gradient(135deg, var(--main-blue) 0%, var(--main-gold) 100%);
        min-height: 100vh;
    }
    
    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        margin-bottom: 0.25rem;
    }
    
    .sidebar .nav-link:hover,
    .sidebar .nav-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .sidebar .nav-link i {
        width: 20px;
    }
    
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        background-color: #f8f9fa;
    }
</style>
@endpush
