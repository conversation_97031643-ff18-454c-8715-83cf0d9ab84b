<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>tie\Permission\Models\Role;
use <PERSON><PERSON>\Permission\Models\Permission;

class AdminRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create admin roles
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        $transportManagerRole = Role::firstOrCreate(['name' => 'transport_manager', 'guard_name' => 'web']);
        $financialManagerRole = Role::firstOrCreate(['name' => 'financial_manager', 'guard_name' => 'web']);
        $operatorRole = Role::firstOrCreate(['name' => 'operator', 'guard_name' => 'web']);

        // Admin permissions (full access to all admin modules)
        $adminPermissions = [
            'read admin_dashboard',
            'read admin_analytics',
            'create admin_clients', 'read admin_clients', 'update admin_clients', 'delete admin_clients',
            'create admin_routes', 'read admin_routes', 'update admin_routes', 'delete admin_routes',
            'create admin_drivers', 'read admin_drivers', 'update admin_drivers', 'delete admin_drivers',
            'create admin_subscriptions', 'read admin_subscriptions', 'update admin_subscriptions', 'delete admin_subscriptions',
            'create admin_payments', 'read admin_payments', 'update admin_payments', 'delete admin_payments',
            'create admin_students', 'read admin_students', 'update admin_students', 'delete admin_students',
            'read admin_financial_reports',
            'read admin_revenue_tracking', 'update admin_revenue_tracking',
            'create admin_expense_management', 'read admin_expense_management', 'update admin_expense_management', 'delete admin_expense_management',
        ];

        // Transport Manager permissions (transport operations only)
        $transportManagerPermissions = [
            'read admin_dashboard',
            'create admin_clients', 'read admin_clients', 'update admin_clients',
            'create admin_routes', 'read admin_routes', 'update admin_routes', 'delete admin_routes',
            'create admin_drivers', 'read admin_drivers', 'update admin_drivers', 'delete admin_drivers',
            'create admin_subscriptions', 'read admin_subscriptions', 'update admin_subscriptions',
            'read admin_payments',
            'create admin_students', 'read admin_students', 'update admin_students',
        ];

        // Financial Manager permissions (financial operations only)
        $financialManagerPermissions = [
            'read admin_dashboard',
            'read admin_analytics',
            'read admin_clients',
            'read admin_subscriptions',
            'create admin_payments', 'read admin_payments', 'update admin_payments',
            'read admin_financial_reports',
            'read admin_revenue_tracking', 'update admin_revenue_tracking',
            'create admin_expense_management', 'read admin_expense_management', 'update admin_expense_management', 'delete admin_expense_management',
        ];

        // Operator permissions (basic operations only)
        $operatorPermissions = [
            'read admin_dashboard',
            'read admin_clients',
            'read admin_routes',
            'read admin_drivers',
            'read admin_subscriptions',
            'read admin_payments',
            'read admin_students',
        ];

        // Assign permissions to roles
        $this->assignPermissionsToRole($adminRole, $adminPermissions);
        $this->assignPermissionsToRole($transportManagerRole, $transportManagerPermissions);
        $this->assignPermissionsToRole($financialManagerRole, $financialManagerPermissions);
        $this->assignPermissionsToRole($operatorRole, $operatorPermissions);
    }

    /**
     * Assign permissions to a role, creating permissions if they don't exist
     */
    private function assignPermissionsToRole(Role $role, array $permissions)
    {
        foreach ($permissions as $permissionName) {
            $permission = Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web'
            ]);
            
            if (!$role->hasPermissionTo($permission)) {
                $role->givePermissionTo($permission);
            }
        }
    }
}
