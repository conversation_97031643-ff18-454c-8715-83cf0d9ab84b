@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        @include('admin.partials.sidebar')

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-car me-2"></i>Driver Management
                </h1>
                @can('create admin_drivers')
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ route('admin.drivers.create') }}" class="btn btn-main">
                        <i class="fas fa-plus me-1"></i>Add New Driver
                    </a>
                </div>
                @endcan
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Drivers</h6>
                                    <h3 class="mb-0">{{ \App\Models\Driver::count() }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Active Drivers</h6>
                                    <h3 class="mb-0">{{ \App\Models\Driver::where('status', 'active')->count() }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Assigned to Routes</h6>
                                    <h3 class="mb-0">{{ \App\Models\Driver::whereHas('activeRoutes')->count() }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-route fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Expiring Licenses</h6>
                                    <h3 class="mb-0">{{ \App\Models\Driver::withExpiringLicense(30)->count() }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <a href="{{ route('admin.drivers.expiring-licenses') }}" class="btn btn-outline-danger">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Expiring Licenses
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <button class="btn btn-outline-info" onclick="showUnassignedDrivers()">
                                            <i class="fas fa-user-slash me-1"></i>Unassigned Drivers
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <button class="btn btn-outline-warning" onclick="showDriverPerformance()">
                                            <i class="fas fa-chart-line me-1"></i>Performance Report
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <a href="#" class="btn btn-outline-success">
                                            <i class="fas fa-download me-1"></i>Export Drivers
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- License Expiry Alerts -->
            @php
                $expiringDrivers = \App\Models\Driver::withExpiringLicense(30)->active()->take(5)->get();
            @endphp
            @if($expiringDrivers->count() > 0)
            <div class="alert alert-warning" role="alert">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>License Expiry Alerts</h6>
                <p class="mb-2">The following drivers have licenses expiring within 30 days:</p>
                <ul class="mb-0">
                    @foreach($expiringDrivers as $driver)
                    <li>
                        <strong>{{ $driver->name }}</strong> - License expires on {{ $driver->license_expiry->format('F j, Y') }}
                        @if($driver->isLicenseExpired())
                            <span class="badge bg-danger ms-1">EXPIRED</span>
                        @elseif($driver->isLicenseExpiringSoon())
                            <span class="badge bg-warning ms-1">EXPIRING SOON</span>
                        @endif
                    </li>
                    @endforeach
                </ul>
                @if($expiringDrivers->count() >= 5)
                <div class="mt-2">
                    <a href="{{ route('admin.drivers.expiring-licenses') }}" class="btn btn-sm btn-outline-danger">
                        View All Expiring Licenses
                    </a>
                </div>
                @endif
            </div>
            @endif

            <!-- DataTable -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Drivers List
                    </h5>
                </div>
                <div class="card-body">
                    {!! $dataTable->table(['class' => 'table table-striped table-hover']) !!}
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Modals -->
<!-- Unassigned Drivers Modal -->
<div class="modal fade" id="unassignedDriversModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-slash me-2"></i>Unassigned Drivers
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="unassignedDriversContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Report Modal -->
<div class="modal fade" id="performanceReportModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-line me-2"></i>Driver Performance Report
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="performanceReportContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
{!! $dataTable->scripts() !!}

<script>
function showUnassignedDrivers() {
    $('#unassignedDriversModal').modal('show');

    // Load unassigned drivers data
    fetch('/admin/drivers/unassigned')
        .then(response => response.json())
        .then(data => {
            let content = '';
            if (data.length === 0) {
                content = '<div class="text-center text-muted"><i class="fas fa-check-circle fa-3x mb-3"></i><p>All active drivers are assigned to routes!</p></div>';
            } else {
                content = '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>Name</th><th>Phone</th><th>License</th><th>Hire Date</th><th>Action</th></tr></thead><tbody>';
                data.forEach(driver => {
                    content += `<tr>
                        <td>${driver.name}</td>
                        <td>${driver.phone}</td>
                        <td>${driver.license_number}</td>
                        <td>${driver.hire_date}</td>
                        <td><a href="/admin/drivers/${driver.id}" class="btn btn-sm btn-outline-primary">View</a></td>
                    </tr>`;
                });
                content += '</tbody></table></div>';
            }
            document.getElementById('unassignedDriversContent').innerHTML = content;
        })
        .catch(error => {
            document.getElementById('unassignedDriversContent').innerHTML = '<div class="alert alert-danger">Error loading data</div>';
        });
}

function showDriverPerformance() {
    $('#performanceReportModal').modal('show');
    // This would load actual performance data
    document.getElementById('performanceReportContent').innerHTML = '<div class="alert alert-info">Performance reporting feature coming soon!</div>';
}
</script>
@endpush
@endsection

@push('styles')
<style>
    .sidebar-custom {
        background: linear-gradient(135deg, var(--main-blue) 0%, var(--main-gold) 100%);
        min-height: 100vh;
    }
    
    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        margin-bottom: 0.25rem;
    }
    
    .sidebar .nav-link:hover,
    .sidebar .nav-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .sidebar .nav-link i {
        width: 20px;
    }
    
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        background-color: #f8f9fa;
    }
    
    .btn-outline-danger:hover,
    .btn-outline-info:hover,
    .btn-outline-warning:hover,
    .btn-outline-success:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
@endpush
