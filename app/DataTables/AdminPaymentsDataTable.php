<?php

namespace App\DataTables;

use App\Models\Payment;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class AdminPaymentsDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-white' : '';
            })
            ->addColumn('actions', function ($row) {
                $actions = '';
                
                if (auth()->user()->can('read admin_payments')) {
                    $actions .= '<a href="' . route('admin.financial.invoice', $row->id) . '" class="btn btn-sm btn-info me-1" title="View Invoice" target="_blank">
                        <i class="fas fa-file-invoice"></i>
                    </a>';
                }
                
                if (auth()->user()->can('update admin_payments')) {
                    $actions .= '<button type="button" class="btn btn-sm btn-warning me-1" title="Update Status" onclick="updatePaymentStatus(' . $row->id . ')">
                        <i class="fas fa-edit"></i>
                    </button>';
                }
                
                return $actions;
            })
            ->editColumn('payment_reference', function ($row) {
                return '<strong>' . $row->payment_reference . '</strong>';
            })
            ->editColumn('client_info', function ($row) {
                $client = $row->subscription->client ?? null;
                if (!$client) return 'N/A';
                
                return '<strong>' . ($client->user->name ?? 'N/A') . '</strong><br>' .
                       '<small class="text-muted">' . ($client->user->phone ?? 'N/A') . '</small>';
            })
            ->editColumn('route_info', function ($row) {
                $route = $row->subscription->route ?? null;
                if (!$route) return 'N/A';
                
                return '<strong>' . $route->name . '</strong><br>' .
                       '<small class="text-muted">' . $route->from_area . ' → ' . $route->to_school . '</small>';
            })
            ->editColumn('amount', function ($row) {
                return '$' . number_format($row->amount, 2);
            })
            ->editColumn('status', function ($row) {
                $badges = [
                    'pending' => 'bg-warning',
                    'completed' => 'bg-success',
                    'failed' => 'bg-danger',
                    'refunded' => 'bg-info'
                ];
                $badgeClass = $badges[$row->status] ?? 'bg-secondary';
                return '<span class="badge ' . $badgeClass . '">' . ucfirst($row->status) . '</span>';
            })
            ->editColumn('payment_method', function ($row) {
                return $row->payment_method_label;
            })
            ->editColumn('payment_date', function ($row) {
                return $row->payment_date->format('Y-m-d');
            })
            ->editColumn('due_date', function ($row) {
                $class = $row->due_date->isPast() && $row->status === 'pending' ? 'text-danger' : '';
                return '<span class="' . $class . '">' . $row->due_date->format('Y-m-d') . '</span>';
            })
            ->addColumn('days_overdue', function ($row) {
                if ($row->status !== 'pending') return 'N/A';
                
                $daysOverdue = $row->due_date->diffInDays(now(), false);
                if ($daysOverdue > 0) {
                    return '<span class="badge bg-danger">' . $daysOverdue . ' days</span>';
                } elseif ($daysOverdue > -7) {
                    return '<span class="badge bg-warning">Due soon</span>';
                } else {
                    return '<span class="text-success">On time</span>';
                }
            })
            ->rawColumns(['actions', 'payment_reference', 'client_info', 'route_info', 'status', 'due_date', 'days_overdue']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Payment $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Payment $model)
    {
        return $model->newQuery()
            ->with(['subscription.client.user', 'subscription.route'])
            ->select('payments.*');
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('admin-payments-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(0, 'desc')
            ->buttons(
                Button::make('export'),
                Button::make('print'),
                Button::make('reload')
            )
            ->language($this->getLang());
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->title('ID')->width(60),
            Column::make('payment_reference')->title('Reference')->searchable(true),
            Column::make('client_info')->title('Client')->orderable(false)->searchable(false),
            Column::make('route_info')->title('Route')->orderable(false)->searchable(false),
            Column::make('amount')->title('Amount')->searchable(false),
            Column::make('payment_method')->title('Method')->searchable(false),
            Column::make('status')->title('Status')->searchable(false),
            Column::make('payment_date')->title('Payment Date'),
            Column::make('due_date')->title('Due Date'),
            Column::make('days_overdue')->title('Status Info')->orderable(false)->searchable(false),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->width(100)
                ->addClass('text-center')
                ->title('Actions'),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'AdminPayments_' . date('YmdHis');
    }

    /**
     * Get language file for DataTable
     */
    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }
}
