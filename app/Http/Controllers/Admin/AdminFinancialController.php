<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Subscription;
use App\Models\Client;
use App\Models\TransportRoute;
use App\DataTables\AdminPaymentsDataTable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminFinancialController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:read admin_financial_reports'])->only(['index', 'reports']);
        $this->middleware(['permission:read admin_revenue_tracking'])->only(['revenue']);
        $this->middleware(['permission:update admin_revenue_tracking'])->only(['updateRevenue']);
        $this->middleware(['permission:read admin_expense_management'])->only(['expenses']);
        $this->middleware(['permission:create admin_expense_management'])->only(['createExpense', 'storeExpense']);
        $this->middleware(['permission:update admin_expense_management'])->only(['editExpense', 'updateExpense']);
        $this->middleware(['permission:delete admin_expense_management'])->only(['destroyExpense']);
    }

    /**
     * Display financial dashboard
     */
    public function index()
    {
        $currentMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();
        
        $stats = [
            'total_revenue' => Payment::where('status', 'completed')->sum('amount'),
            'monthly_revenue' => Payment::where('status', 'completed')
                ->whereMonth('payment_date', now()->month)
                ->whereYear('payment_date', now()->year)
                ->sum('amount'),
            'pending_payments' => Payment::where('status', 'pending')->sum('amount'),
            'overdue_payments' => Payment::where('status', 'pending')
                ->where('due_date', '<', now())
                ->sum('amount'),
            'active_subscriptions' => Subscription::where('status', 'active')->count(),
            'total_clients' => Client::count(),
        ];

        // Monthly revenue trend (last 6 months)
        $monthlyRevenue = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $revenue = Payment::where('status', 'completed')
                ->whereMonth('payment_date', $month->month)
                ->whereYear('payment_date', $month->year)
                ->sum('amount');
            
            $monthlyRevenue[] = [
                'month' => $month->format('M Y'),
                'revenue' => $revenue
            ];
        }

        // Recent payments
        $recentPayments = Payment::with(['subscription.client.user'])
            ->latest()
            ->take(10)
            ->get();

        // Overdue payments
        $overduePayments = Payment::with(['subscription.client.user'])
            ->where('status', 'pending')
            ->where('due_date', '<', now())
            ->orderBy('due_date')
            ->take(10)
            ->get();

        return view('admin.financial.index', compact(
            'stats', 
            'monthlyRevenue', 
            'recentPayments', 
            'overduePayments'
        ));
    }

    /**
     * Display detailed financial reports
     */
    public function reports(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));
        $routeId = $request->get('route_id');

        $query = Payment::with(['subscription.client.user', 'subscription.route'])
            ->whereBetween('payment_date', [$startDate, $endDate]);

        if ($routeId) {
            $query->whereHas('subscription', function ($q) use ($routeId) {
                $q->where('route_id', $routeId);
            });
        }

        $payments = $query->orderBy('payment_date', 'desc')->paginate(20);

        // Summary statistics
        $summary = [
            'total_amount' => $query->where('status', 'completed')->sum('amount'),
            'pending_amount' => $query->where('status', 'pending')->sum('amount'),
            'completed_count' => $query->where('status', 'completed')->count(),
            'pending_count' => $query->where('status', 'pending')->count(),
        ];

        // Revenue by route
        $revenueByRoute = Payment::select('routes.name', DB::raw('SUM(payments.amount) as total'))
            ->join('subscriptions', 'payments.subscription_id', '=', 'subscriptions.id')
            ->join('routes', 'subscriptions.route_id', '=', 'routes.id')
            ->where('payments.status', 'completed')
            ->whereBetween('payments.payment_date', [$startDate, $endDate])
            ->groupBy('routes.id', 'routes.name')
            ->orderBy('total', 'desc')
            ->get();

        $routes = TransportRoute::active()->get();

        return view('admin.financial.reports', compact(
            'payments', 
            'summary', 
            'revenueByRoute', 
            'routes',
            'startDate',
            'endDate',
            'routeId'
        ));
    }

    /**
     * Display revenue tracking
     */
    public function revenue(Request $request)
    {
        $year = $request->get('year', now()->year);
        $routeId = $request->get('route_id');

        // Monthly revenue for the year
        $monthlyData = [];
        for ($month = 1; $month <= 12; $month++) {
            $query = Payment::where('status', 'completed')
                ->whereMonth('payment_date', $month)
                ->whereYear('payment_date', $year);

            if ($routeId) {
                $query->whereHas('subscription', function ($q) use ($routeId) {
                    $q->where('route_id', $routeId);
                });
            }

            $monthlyData[] = [
                'month' => Carbon::create($year, $month, 1)->format('M'),
                'revenue' => $query->sum('amount'),
                'count' => $query->count(),
            ];
        }

        // Top performing routes
        $topRoutes = Payment::select('routes.name', DB::raw('SUM(payments.amount) as total'))
            ->join('subscriptions', 'payments.subscription_id', '=', 'subscriptions.id')
            ->join('routes', 'subscriptions.route_id', '=', 'routes.id')
            ->where('payments.status', 'completed')
            ->whereYear('payments.payment_date', $year)
            ->groupBy('routes.id', 'routes.name')
            ->orderBy('total', 'desc')
            ->take(10)
            ->get();

        $routes = TransportRoute::active()->get();

        return view('admin.financial.revenue', compact(
            'monthlyData',
            'topRoutes',
            'routes',
            'year',
            'routeId'
        ));
    }

    /**
     * Display payment management
     */
    public function payments(AdminPaymentsDataTable $dataTable)
    {
        return $dataTable->render('admin.financial.payments');
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus(Request $request, Payment $payment)
    {
        $request->validate([
            'status' => 'required|in:pending,completed,failed,refunded',
            'notes' => 'nullable|string',
        ]);

        $payment->update([
            'status' => $request->status,
            'notes' => $request->notes,
        ]);

        return redirect()->back()
            ->with('success', 'Payment status updated successfully.');
    }

    /**
     * Generate invoice for payment
     */
    public function generateInvoice(Payment $payment)
    {
        $payment->load(['subscription.client.user', 'subscription.route']);
        
        return view('admin.financial.invoice', compact('payment'));
    }

    /**
     * Export financial data
     */
    public function export(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));
        
        $payments = Payment::with(['subscription.client.user', 'subscription.route'])
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->orderBy('payment_date', 'desc')
            ->get();

        // Here you would implement the actual export logic (Excel, PDF, etc.)
        // For now, return a simple CSV response
        
        $filename = "financial_report_{$startDate}_to_{$endDate}.csv";
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($payments) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Payment ID',
                'Client Name',
                'Route',
                'Amount',
                'Status',
                'Payment Date',
                'Due Date',
                'Payment Method'
            ]);

            // CSV data
            foreach ($payments as $payment) {
                fputcsv($file, [
                    $payment->id,
                    $payment->subscription->client->user->name,
                    $payment->subscription->route->name,
                    $payment->amount,
                    $payment->status,
                    $payment->payment_date->format('Y-m-d'),
                    $payment->due_date->format('Y-m-d'),
                    $payment->payment_method
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
