<?php

use App\Http\Controllers\ClientController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\SubscriptionController;
use App\Http\Controllers\Admin\PaymentController;

/*
  |--------------------------------------------------------------------------
  | Web Routes
  |--------------------------------------------------------------------------
  |
  | Here is where you can register web routes for your application. These
  | routes are loaded by the RouteServiceProvider within a group which
  | contains the "web" middleware group. Now create something great!
  |
 */
Route::get('/seed', function () {
    Artisan::call("db:seed --class=ResetPermissionSeeder");
});

// Authentication Routes (Jetstream automatically registers these)
// But we need to define the dashboard route
Route::middleware(['auth:sanctum', 'verified'])->get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

Route::group(['middleware' => ['web', 'locale']], function () {
    // home page
    Route::get('/', function () {
        $stats = [
            'completed_journeys' => 1200,
            'satisfied_clients' => 350,
            'years_experience' => 8,
        ];
        return view('welcome', compact('stats'));
    })->name('home');
    
    // client registration
    Route::get('/clients/register', [ClientController::class, 'create'])->name('clients.create');
    Route::post('/clients/register', [ClientController::class, 'store'])->name('clients.store');
    Route::get('/thankyou', function () {
        return view('clients.thankyou');
    })->name('clients.thankyou');
    
    // Contact form submission
    Route::post('/contact', function () {
        // You can handle the contact form logic here or point to a controller
        // For now, just redirect back with a success message
        return back()->with('success', __('messages.contact_form_sent') ?? 'Your message has been sent!');
    })->name('contact.submit');
    
    // Test translation route
    Route::get('/test-translation', function () {
        return __('welcome_to_royal_transit');
    });
    
    // Test translation route for array-based translation
    Route::get('/test-translation-array', function () {
        return __('messages.welcome_to_royal_transit');
    });
    
    // Admin routes
    Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
        // Dashboard - accessible to all admin users
        Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

        // Client Management Routes
        Route::middleware(['permission:view admin_clients'])->group(function () {
            Route::get('/clients', [\App\Http\Controllers\Admin\AdminClientController::class, 'index'])->name('clients.index');
            Route::get('/clients/{client}', [\App\Http\Controllers\Admin\AdminClientController::class, 'show'])->name('clients.show');
        });

        Route::middleware(['permission:create admin_clients'])->group(function () {
            Route::get('/clients/create', [\App\Http\Controllers\Admin\AdminClientController::class, 'create'])->name('clients.create');
            Route::post('/clients', [\App\Http\Controllers\Admin\AdminClientController::class, 'store'])->name('clients.store');
        });

        Route::middleware(['permission:update admin_clients'])->group(function () {
            Route::get('/clients/{client}/edit', [\App\Http\Controllers\Admin\AdminClientController::class, 'edit'])->name('clients.edit');
            Route::put('/clients/{client}', [\App\Http\Controllers\Admin\AdminClientController::class, 'update'])->name('clients.update');
        });

        Route::middleware(['permission:delete admin_clients'])->group(function () {
            Route::delete('/clients/{client}', [\App\Http\Controllers\Admin\AdminClientController::class, 'destroy'])->name('clients.destroy');
        });

        // Route Management Routes
        Route::middleware(['permission:view admin_routes'])->group(function () {
            Route::get('/routes', [\App\Http\Controllers\Admin\AdminRouteController::class, 'index'])->name('routes.index');
            Route::get('/routes/{route}', [\App\Http\Controllers\Admin\AdminRouteController::class, 'show'])->name('routes.show');
        });

        Route::middleware(['permission:create admin_routes'])->group(function () {
            Route::get('/routes/create', [\App\Http\Controllers\Admin\AdminRouteController::class, 'create'])->name('routes.create');
            Route::post('/routes', [\App\Http\Controllers\Admin\AdminRouteController::class, 'store'])->name('routes.store');
        });

        Route::middleware(['permission:update admin_routes'])->group(function () {
            Route::get('/routes/{route}/edit', [\App\Http\Controllers\Admin\AdminRouteController::class, 'edit'])->name('routes.edit');
            Route::put('/routes/{route}', [\App\Http\Controllers\Admin\AdminRouteController::class, 'update'])->name('routes.update');
        });

        Route::middleware(['permission:delete admin_routes'])->group(function () {
            Route::delete('/routes/{route}', [\App\Http\Controllers\Admin\AdminRouteController::class, 'destroy'])->name('routes.destroy');
        });

        // Driver Management Routes
        Route::middleware(['permission:view admin_drivers'])->group(function () {
            Route::get('/drivers', [\App\Http\Controllers\Admin\AdminDriverController::class, 'index'])->name('drivers.index');
            Route::get('/drivers/{driver}', [\App\Http\Controllers\Admin\AdminDriverController::class, 'show'])->name('drivers.show');
            Route::get('/drivers/expiring-licenses', [\App\Http\Controllers\Admin\AdminDriverController::class, 'expiringLicenses'])->name('drivers.expiring-licenses');
        });

        Route::middleware(['permission:create admin_drivers'])->group(function () {
            Route::get('/drivers/create', [\App\Http\Controllers\Admin\AdminDriverController::class, 'create'])->name('drivers.create');
            Route::post('/drivers', [\App\Http\Controllers\Admin\AdminDriverController::class, 'store'])->name('drivers.store');
        });

        Route::middleware(['permission:update admin_drivers'])->group(function () {
            Route::get('/drivers/{driver}/edit', [\App\Http\Controllers\Admin\AdminDriverController::class, 'edit'])->name('drivers.edit');
            Route::put('/drivers/{driver}', [\App\Http\Controllers\Admin\AdminDriverController::class, 'update'])->name('drivers.update');
            Route::post('/drivers/{driver}/assign-route', [\App\Http\Controllers\Admin\AdminDriverController::class, 'assignRoute'])->name('drivers.assign-route');
            Route::post('/drivers/{driver}/remove-route', [\App\Http\Controllers\Admin\AdminDriverController::class, 'removeRoute'])->name('drivers.remove-route');
            Route::post('/drivers/{driver}/suspend', [\App\Http\Controllers\Admin\AdminDriverController::class, 'suspend'])->name('drivers.suspend');
            Route::post('/drivers/{driver}/activate', [\App\Http\Controllers\Admin\AdminDriverController::class, 'activate'])->name('drivers.activate');
            Route::post('/drivers/{driver}/routes/{route}/remove', [\App\Http\Controllers\Admin\AdminDriverController::class, 'removeFromSpecificRoute'])->name('drivers.routes.remove');
        });

        Route::middleware(['permission:delete admin_drivers'])->group(function () {
            Route::delete('/drivers/{driver}', [\App\Http\Controllers\Admin\AdminDriverController::class, 'destroy'])->name('drivers.destroy');
        });

        // Financial Management Routes
        Route::middleware(['permission:view admin_financial'])->group(function () {
            Route::get('/financial', [\App\Http\Controllers\Admin\AdminFinancialController::class, 'index'])->name('financial.index');
            Route::get('/financial/reports', [\App\Http\Controllers\Admin\AdminFinancialController::class, 'reports'])->name('financial.reports');
            Route::get('/financial/revenue', [\App\Http\Controllers\Admin\AdminFinancialController::class, 'revenue'])->name('financial.revenue');
            Route::get('/financial/payments', [\App\Http\Controllers\Admin\AdminFinancialController::class, 'payments'])->name('financial.payments');
        });

        Route::middleware(['permission:update admin_financial'])->group(function () {
            Route::post('/financial/payments/{payment}/mark-paid', [\App\Http\Controllers\Admin\AdminFinancialController::class, 'markPaid'])->name('financial.payments.mark-paid');
            Route::post('/financial/payments/{payment}/mark-overdue', [\App\Http\Controllers\Admin\AdminFinancialController::class, 'markOverdue'])->name('financial.payments.mark-overdue');
        });

        // DataTable Routes (AJAX endpoints)
        Route::middleware(['permission:view admin_clients'])->group(function () {
            Route::get('/clients-data', [\App\DataTables\AdminClientsDataTable::class, 'ajax'])->name('clients.data');
        });

        Route::middleware(['permission:view admin_routes'])->group(function () {
            Route::get('/routes-data', [\App\DataTables\AdminRoutesDataTable::class, 'ajax'])->name('routes.data');
        });

        Route::middleware(['permission:view admin_drivers'])->group(function () {
            Route::get('/drivers-data', [\App\DataTables\AdminDriversDataTable::class, 'ajax'])->name('drivers.data');
        });

        // Legacy routes (keeping for backward compatibility)
        Route::middleware(['permission:view admin_subscriptions'])->group(function () {
            Route::resource('subscriptions', SubscriptionController::class);
        });

        Route::middleware(['permission:view admin_payments'])->group(function () {
            Route::resource('payments', PaymentController::class);
        });
    });
});
