<?php

namespace App\DataTables;

use App\Models\TransportRoute;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class AdminRoutesDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-white' : '';
            })
            ->addColumn('actions', function ($row) {
                $actions = '';
                
                if (auth()->user()->can('read admin_routes')) {
                    $actions .= '<a href="' . route('admin.routes.show', $row->id) . '" class="btn btn-sm btn-info me-1" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';
                }
                
                if (auth()->user()->can('update admin_routes')) {
                    $actions .= '<a href="' . route('admin.routes.edit', $row->id) . '" class="btn btn-sm btn-warning me-1" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';
                }
                
                if (auth()->user()->can('delete admin_routes')) {
                    $actions .= '<form method="POST" action="' . route('admin.routes.destroy', $row->id) . '" style="display: inline;" onsubmit="return confirm(\'Are you sure?\')">
                        ' . csrf_field() . '
                        ' . method_field('DELETE') . '
                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </form>';
                }
                
                return $actions;
            })
            ->editColumn('name', function ($row) {
                return '<strong>' . $row->name . '</strong><br><small class="text-muted">' . $row->from_area . ' → ' . $row->to_school . '</small>';
            })
            ->editColumn('schedule', function ($row) {
                return 'Pickup: ' . $row->pickup_time->format('H:i') . '<br>Dropoff: ' . $row->dropoff_time->format('H:i');
            })
            ->editColumn('capacity_info', function ($row) {
                $percentage = $row->capacity_percentage;
                $badgeClass = $percentage >= 90 ? 'bg-danger' : ($percentage >= 70 ? 'bg-warning' : 'bg-success');
                
                return $row->current_students . '/' . $row->capacity . 
                       ' <span class="badge ' . $badgeClass . '">' . round($percentage) . '%</span>';
            })
            ->editColumn('monthly_price', function ($row) {
                return '$' . number_format($row->monthly_price, 2);
            })
            ->editColumn('status', function ($row) {
                $badgeClass = $row->status === 'active' ? 'bg-success' : 'bg-secondary';
                return '<span class="badge ' . $badgeClass . '">' . ucfirst($row->status) . '</span>';
            })
            ->addColumn('driver', function ($row) {
                $driver = $row->currentDriver();
                return $driver ? $driver->name : '<span class="text-muted">No driver assigned</span>';
            })
            ->editColumn('created_at', function ($row) {
                return $row->created_at->format('Y-m-d H:i');
            })
            ->rawColumns(['actions', 'name', 'schedule', 'capacity_info', 'status', 'driver']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\TransportRoute $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(TransportRoute $model)
    {
        return $model->newQuery()
            ->with(['subscriptions', 'activeDrivers'])
            ->select('routes.*');
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('admin-routes-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(0, 'desc')
            ->buttons(
                Button::make('create')
                    ->text('<i class="fas fa-plus"></i> Add Route')
                    ->className('btn btn-primary')
                    ->action('window.location.href = "' . route('admin.routes.create') . '"'),
                Button::make('export'),
                Button::make('print'),
                Button::make('reload')
            )
            ->language($this->getLang());
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->title('ID')->width(60),
            Column::make('name')->title('Route Info')->searchable(true)->orderable(false),
            Column::make('schedule')->title('Schedule')->orderable(false)->searchable(false),
            Column::make('capacity_info')->title('Capacity')->orderable(false)->searchable(false),
            Column::make('monthly_price')->title('Price')->searchable(false),
            Column::make('driver')->title('Driver')->orderable(false)->searchable(false),
            Column::make('status')->title('Status')->searchable(false),
            Column::make('created_at')->title('Created At'),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->width(120)
                ->addClass('text-center')
                ->title('Actions'),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'AdminRoutes_' . date('YmdHis');
    }

    /**
     * Get language file for DataTable
     */
    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }
}
