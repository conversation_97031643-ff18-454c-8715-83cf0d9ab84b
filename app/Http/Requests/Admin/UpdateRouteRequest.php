<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRouteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->user()->can('update admin_routes');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $routeId = $this->route('route') ? $this->route('route')->id : null;
        
        return [
            'route_name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('routes', 'route_name')->ignore($routeId)
            ],
            'start_location' => 'required|string|max:255',
            'end_location' => 'required|string|max:255',
            'distance_km' => 'required|numeric|min:0.1|max:999.99',
            'estimated_duration_minutes' => 'required|integer|min:1|max:999',
            'route_type' => 'required|in:pickup,dropoff,both',
            'status' => 'required|in:active,inactive,maintenance',
            'description' => 'nullable|string|max:1000',
            'max_capacity' => 'required|integer|min:1|max:50',
            'price_per_km' => 'required|numeric|min:0.01|max:999.99',
            'base_price' => 'required|numeric|min:0.01|max:9999.99',
            
            // Route stops (array validation)
            'stops' => 'nullable|array|max:20',
            'stops.*.id' => 'nullable|exists:route_stops,id',
            'stops.*.stop_name' => 'required_with:stops|string|max:255',
            'stops.*.stop_order' => 'required_with:stops|integer|min:1',
            'stops.*.latitude' => 'nullable|numeric|between:-90,90',
            'stops.*.longitude' => 'nullable|numeric|between:-180,180',
            'stops.*.estimated_arrival_time' => 'nullable|date_format:H:i',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'route_name.required' => __('messages.required_field'),
            'route_name.unique' => __('Route name already exists'),
            'route_name.max' => __('messages.max_length', ['max' => 255]),
            'start_location.required' => __('messages.required_field'),
            'start_location.max' => __('messages.max_length', ['max' => 255]),
            'end_location.required' => __('messages.required_field'),
            'end_location.max' => __('messages.max_length', ['max' => 255]),
            'distance_km.required' => __('messages.required_field'),
            'distance_km.numeric' => __('Distance must be a valid number'),
            'distance_km.min' => __('Distance must be at least 0.1 km'),
            'distance_km.max' => __('Distance cannot exceed 999.99 km'),
            'estimated_duration_minutes.required' => __('messages.required_field'),
            'estimated_duration_minutes.integer' => __('Duration must be a whole number'),
            'estimated_duration_minutes.min' => __('Duration must be at least 1 minute'),
            'estimated_duration_minutes.max' => __('Duration cannot exceed 999 minutes'),
            'route_type.required' => __('messages.required_field'),
            'route_type.in' => __('Please select a valid route type'),
            'status.required' => __('messages.required_field'),
            'status.in' => __('Please select a valid status'),
            'description.max' => __('messages.max_length', ['max' => 1000]),
            'max_capacity.required' => __('messages.required_field'),
            'max_capacity.integer' => __('Capacity must be a whole number'),
            'max_capacity.min' => __('Capacity must be at least 1'),
            'max_capacity.max' => __('Capacity cannot exceed 50'),
            'price_per_km.required' => __('messages.required_field'),
            'price_per_km.numeric' => __('Price per km must be a valid number'),
            'price_per_km.min' => __('Price per km must be at least 0.01'),
            'price_per_km.max' => __('Price per km cannot exceed 999.99'),
            'base_price.required' => __('messages.required_field'),
            'base_price.numeric' => __('Base price must be a valid number'),
            'base_price.min' => __('Base price must be at least 0.01'),
            'base_price.max' => __('Base price cannot exceed 9999.99'),
            
            // Stops validation messages
            'stops.max' => __('Maximum 20 stops allowed'),
            'stops.*.id.exists' => __('Invalid stop selected'),
            'stops.*.stop_name.required_with' => __('Stop name is required'),
            'stops.*.stop_name.max' => __('messages.max_length', ['max' => 255]),
            'stops.*.stop_order.required_with' => __('Stop order is required'),
            'stops.*.stop_order.integer' => __('Stop order must be a whole number'),
            'stops.*.stop_order.min' => __('Stop order must be at least 1'),
            'stops.*.latitude.numeric' => __('Latitude must be a valid number'),
            'stops.*.latitude.between' => __('Latitude must be between -90 and 90'),
            'stops.*.longitude.numeric' => __('Longitude must be a valid number'),
            'stops.*.longitude.between' => __('Longitude must be between -180 and 180'),
            'stops.*.estimated_arrival_time.date_format' => __('Please enter a valid time format (HH:MM)'),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'route_name' => __('messages.route_name'),
            'start_location' => __('messages.start_location'),
            'end_location' => __('messages.end_location'),
            'distance_km' => __('messages.distance'),
            'estimated_duration_minutes' => __('messages.estimated_duration'),
            'route_type' => __('messages.route_type'),
            'status' => __('messages.status'),
            'description' => __('messages.description'),
            'max_capacity' => __('messages.max_capacity'),
            'price_per_km' => __('messages.price_per_km'),
            'base_price' => __('messages.base_price'),
            'stops.*.stop_name' => __('messages.stop_name'),
            'stops.*.stop_order' => __('messages.stop_order'),
            'stops.*.latitude' => __('messages.latitude'),
            'stops.*.longitude' => __('messages.longitude'),
            'stops.*.estimated_arrival_time' => __('messages.estimated_arrival_time'),
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Ensure stops is an array
        if ($this->has('stops') && !is_array($this->input('stops'))) {
            $this->merge(['stops' => []]);
        }

        // Clean numeric fields
        $numericFields = ['distance_km', 'price_per_km', 'base_price'];
        foreach ($numericFields as $field) {
            if ($this->has($field)) {
                $value = $this->input($field);
                // Remove any non-numeric characters except decimal point
                $cleanValue = preg_replace('/[^0-9.]/', '', $value);
                $this->merge([$field => $cleanValue]);
            }
        }
    }
}
