<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TransportRoute;
use App\Models\Driver;
use App\DataTables\AdminRoutesDataTable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminRouteController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:read admin_routes'])->only(['index', 'show']);
        $this->middleware(['permission:create admin_routes'])->only(['create', 'store']);
        $this->middleware(['permission:update admin_routes'])->only(['edit', 'update']);
        $this->middleware(['permission:delete admin_routes'])->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(AdminRoutesDataTable $dataTable)
    {
        return $dataTable->render('admin.routes.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $drivers = Driver::active()->get();
        
        return view('admin.routes.create', compact('drivers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'from_area' => 'required|string|max:255',
            'to_school' => 'required|string|max:255',
            'pickup_time' => 'required|date_format:H:i',
            'dropoff_time' => 'required|date_format:H:i',
            'monthly_price' => 'required|numeric|min:0',
            'capacity' => 'required|integer|min:1',
            'status' => 'required|in:active,inactive',
            'description' => 'nullable|string',
            'stops' => 'nullable|array',
            'stops.*' => 'string|max:255',
            'driver_id' => 'nullable|exists:drivers,id',
        ]);

        DB::transaction(function () use ($request) {
            $route = TransportRoute::create([
                'name' => $request->name,
                'from_area' => $request->from_area,
                'to_school' => $request->to_school,
                'pickup_time' => $request->pickup_time,
                'dropoff_time' => $request->dropoff_time,
                'monthly_price' => $request->monthly_price,
                'capacity' => $request->capacity,
                'current_students' => 0,
                'status' => $request->status,
                'description' => $request->description,
                'stops' => $request->stops ? array_filter($request->stops) : null,
            ]);

            // Assign driver if selected
            if ($request->driver_id) {
                $route->drivers()->attach($request->driver_id, [
                    'assigned_date' => now(),
                    'status' => 'active',
                ]);
            }
        });

        return redirect()->route('admin.routes.index')
            ->with('success', 'Route created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(TransportRoute $route)
    {
        $route->load(['subscriptions.client.user', 'activeDrivers', 'drivers']);
        
        return view('admin.routes.show', compact('route'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TransportRoute $route)
    {
        $drivers = Driver::active()->get();
        $route->load('activeDrivers');
        
        return view('admin.routes.edit', compact('route', 'drivers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TransportRoute $route)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'from_area' => 'required|string|max:255',
            'to_school' => 'required|string|max:255',
            'pickup_time' => 'required|date_format:H:i',
            'dropoff_time' => 'required|date_format:H:i',
            'monthly_price' => 'required|numeric|min:0',
            'capacity' => 'required|integer|min:1',
            'status' => 'required|in:active,inactive',
            'description' => 'nullable|string',
            'stops' => 'nullable|array',
            'stops.*' => 'string|max:255',
            'driver_id' => 'nullable|exists:drivers,id',
        ]);

        DB::transaction(function () use ($request, $route) {
            $route->update([
                'name' => $request->name,
                'from_area' => $request->from_area,
                'to_school' => $request->to_school,
                'pickup_time' => $request->pickup_time,
                'dropoff_time' => $request->dropoff_time,
                'monthly_price' => $request->monthly_price,
                'capacity' => $request->capacity,
                'status' => $request->status,
                'description' => $request->description,
                'stops' => $request->stops ? array_filter($request->stops) : null,
            ]);

            // Update driver assignment
            $currentDriver = $route->currentDriver();
            
            if ($request->driver_id) {
                if (!$currentDriver || $currentDriver->id != $request->driver_id) {
                    // End current driver assignment
                    if ($currentDriver) {
                        $route->drivers()->updateExistingPivot($currentDriver->id, [
                            'status' => 'inactive',
                            'end_date' => now(),
                        ]);
                    }
                    
                    // Assign new driver
                    $route->drivers()->attach($request->driver_id, [
                        'assigned_date' => now(),
                        'status' => 'active',
                    ]);
                }
            } else {
                // Remove current driver assignment
                if ($currentDriver) {
                    $route->drivers()->updateExistingPivot($currentDriver->id, [
                        'status' => 'inactive',
                        'end_date' => now(),
                    ]);
                }
            }
        });

        return redirect()->route('admin.routes.index')
            ->with('success', 'Route updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TransportRoute $route)
    {
        if ($route->subscriptions()->exists()) {
            return redirect()->route('admin.routes.index')
                ->with('error', 'Cannot delete route with active subscriptions.');
        }

        DB::transaction(function () use ($route) {
            // End all driver assignments
            $route->drivers()->updateExistingPivot(
                $route->drivers()->pluck('drivers.id')->toArray(),
                ['status' => 'inactive', 'end_date' => now()]
            );
            
            $route->delete();
        });

        return redirect()->route('admin.routes.index')
            ->with('success', 'Route deleted successfully.');
    }

    /**
     * Assign driver to route
     */
    public function assignDriver(Request $request, TransportRoute $route)
    {
        $request->validate([
            'driver_id' => 'required|exists:drivers,id',
        ]);

        $currentDriver = $route->currentDriver();
        
        if ($currentDriver) {
            return redirect()->back()
                ->with('error', 'Route already has an assigned driver.');
        }

        $route->drivers()->attach($request->driver_id, [
            'assigned_date' => now(),
            'status' => 'active',
        ]);

        return redirect()->back()
            ->with('success', 'Driver assigned successfully.');
    }

    /**
     * Remove driver from route
     */
    public function removeDriver(TransportRoute $route, Driver $driver)
    {
        $route->drivers()->updateExistingPivot($driver->id, [
            'status' => 'inactive',
            'end_date' => now(),
        ]);

        return redirect()->back()
            ->with('success', 'Driver removed from route.');
    }
}
