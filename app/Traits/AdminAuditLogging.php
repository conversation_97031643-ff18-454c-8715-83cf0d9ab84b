<?php

namespace App\Traits;

use App\Models\AuditLog;
use Illuminate\Http\Request;

trait AdminAuditLogging
{
    /**
     * Log admin action with detailed information
     *
     * @param string $action
     * @param mixed $model
     * @param array $changes
     * @param Request $request
     * @return void
     */
    protected function logAdminAction(string $action, $model = null, array $changes = [], Request $request = null)
    {
        $request = $request ?: request();
        $user = auth()->user();
        
        if (!$user) {
            return;
        }

        $description = $this->generateActionDescription($action, $model, $changes);
        
        AuditLog::create([
            'user_id' => $user->id,
            'description' => $description,
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => $model ? get_class($model) : null,
            'relationmodel_id' => $model ? $model->id : null,
            'type' => $this->getActionType($action),
        ]);
    }

    /**
     * Generate detailed description for the action
     *
     * @param string $action
     * @param mixed $model
     * @param array $changes
     * @return string
     */
    private function generateActionDescription(string $action, $model, array $changes): string
    {
        $modelName = $model ? class_basename($model) : 'Unknown';
        $modelId = $model ? $model->id : 'N/A';
        
        $description = "Admin {$action} action on {$modelName} (ID: {$modelId})";
        
        // Add specific details based on action
        switch ($action) {
            case 'created':
                $description .= $this->getCreatedDescription($model);
                break;
            case 'updated':
                $description .= $this->getUpdatedDescription($changes);
                break;
            case 'deleted':
                $description .= $this->getDeletedDescription($model);
                break;
            case 'viewed':
                $description .= " - Record accessed";
                break;
            case 'exported':
                $description .= " - Data exported";
                break;
        }

        return $description;
    }

    /**
     * Get description for created action
     *
     * @param mixed $model
     * @return string
     */
    private function getCreatedDescription($model): string
    {
        if (!$model) return '';
        
        $details = [];
        
        // Add model-specific details
        switch (class_basename($model)) {
            case 'Client':
                $details[] = "Name: " . ($model->user->name ?? 'N/A');
                $details[] = "Phone: " . ($model->user->phone ?? 'N/A');
                break;
            case 'Route':
                $details[] = "Route Name: " . ($model->route_name ?? 'N/A');
                $details[] = "From: " . ($model->from_location ?? 'N/A');
                $details[] = "To: " . ($model->to_location ?? 'N/A');
                break;
            case 'Driver':
                $details[] = "Name: " . ($model->name ?? 'N/A');
                $details[] = "License: " . ($model->license_number ?? 'N/A');
                break;
            case 'User':
                $details[] = "Name: " . ($model->name ?? 'N/A');
                $details[] = "Email: " . ($model->email ?? 'N/A');
                break;
        }
        
        return $details ? ' - ' . implode(', ', $details) : '';
    }

    /**
     * Get description for updated action
     *
     * @param array $changes
     * @return string
     */
    private function getUpdatedDescription(array $changes): string
    {
        if (empty($changes)) return '';
        
        $changedFields = [];
        foreach ($changes as $field => $values) {
            if (is_array($values) && isset($values['old'], $values['new'])) {
                $changedFields[] = "{$field}: '{$values['old']}' → '{$values['new']}'";
            } else {
                $changedFields[] = "{$field}: updated";
            }
        }
        
        return $changedFields ? ' - Changes: ' . implode(', ', $changedFields) : '';
    }

    /**
     * Get description for deleted action
     *
     * @param mixed $model
     * @return string
     */
    private function getDeletedDescription($model): string
    {
        if (!$model) return '';
        
        $identifier = '';
        switch (class_basename($model)) {
            case 'Client':
                $identifier = $model->user->name ?? 'Unknown Client';
                break;
            case 'Route':
                $identifier = $model->route_name ?? 'Unknown Route';
                break;
            case 'Driver':
                $identifier = $model->name ?? 'Unknown Driver';
                break;
            case 'User':
                $identifier = $model->name ?? 'Unknown User';
                break;
            default:
                $identifier = "ID: {$model->id}";
        }
        
        return " - Deleted: {$identifier}";
    }

    /**
     * Get action type for categorization
     *
     * @param string $action
     * @return int
     */
    private function getActionType(string $action): int
    {
        $types = [
            'created' => 1,  // CREATE
            'updated' => 2,  // UPDATE
            'deleted' => 3,  // DELETE
            'viewed' => 4,   // READ
            'exported' => 5, // EXPORT
            'imported' => 6, // IMPORT
        ];
        
        return $types[$action] ?? 7; // OTHER
    }

    /**
     * Log model changes for update operations
     *
     * @param mixed $model
     * @param array $originalData
     * @return array
     */
    protected function getModelChanges($model, array $originalData = []): array
    {
        if (!$model || !$model->wasChanged()) {
            return [];
        }
        
        $changes = [];
        foreach ($model->getChanges() as $field => $newValue) {
            $oldValue = $originalData[$field] ?? $model->getOriginal($field);
            
            // Skip sensitive fields
            if (in_array($field, ['password', 'remember_token', 'two_factor_secret'])) {
                continue;
            }
            
            $changes[$field] = [
                'old' => $oldValue,
                'new' => $newValue
            ];
        }
        
        return $changes;
    }

    /**
     * Log bulk operations
     *
     * @param string $action
     * @param string $modelType
     * @param int $count
     * @param array $additionalInfo
     * @return void
     */
    protected function logBulkAction(string $action, string $modelType, int $count, array $additionalInfo = [])
    {
        $description = "Admin bulk {$action} action on {$count} {$modelType} records";
        
        if (!empty($additionalInfo)) {
            $description .= ' - ' . implode(', ', $additionalInfo);
        }
        
        // Get action type using same mapping as getActionType
        $types = [
            'created' => 1,  // CREATE
            'updated' => 2,  // UPDATE
            'deleted' => 3,  // DELETE
            'viewed' => 4,   // READ
            'exported' => 5, // EXPORT
            'imported' => 6, // IMPORT
        ];
        $actionType = $types[$action] ?? 7; // OTHER
        
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => $description,
            'ip_address' => request()->getClientIp(),
            'relationmodel_type' => $modelType,
            'type' => $actionType,
        ]);
    }
}
