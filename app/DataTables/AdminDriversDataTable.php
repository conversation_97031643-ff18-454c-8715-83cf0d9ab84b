<?php

namespace App\DataTables;

use App\Models\Driver;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class AdminDriversDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-white' : '';
            })
            ->addColumn('actions', function ($row) {
                $actions = '';
                
                if (auth()->user()->can('read admin_drivers')) {
                    $actions .= '<a href="' . route('admin.drivers.show', $row->id) . '" class="btn btn-sm btn-info me-1" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';
                }
                
                if (auth()->user()->can('update admin_drivers')) {
                    $actions .= '<a href="' . route('admin.drivers.edit', $row->id) . '" class="btn btn-sm btn-warning me-1" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';
                    
                    // Status toggle buttons
                    if ($row->status === 'active') {
                        $actions .= '<form method="POST" action="' . route('admin.drivers.suspend', $row->id) . '" style="display: inline;">
                            ' . csrf_field() . '
                            <button type="submit" class="btn btn-sm btn-warning me-1" title="Suspend" onclick="return confirm(\'Suspend this driver?\')">
                                <i class="fas fa-pause"></i>
                            </button>
                        </form>';
                    } else {
                        $actions .= '<form method="POST" action="' . route('admin.drivers.activate', $row->id) . '" style="display: inline;">
                            ' . csrf_field() . '
                            <button type="submit" class="btn btn-sm btn-success me-1" title="Activate">
                                <i class="fas fa-play"></i>
                            </button>
                        </form>';
                    }
                }
                
                if (auth()->user()->can('delete admin_drivers')) {
                    $actions .= '<form method="POST" action="' . route('admin.drivers.destroy', $row->id) . '" style="display: inline;" onsubmit="return confirm(\'Are you sure?\')">
                        ' . csrf_field() . '
                        ' . method_field('DELETE') . '
                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </form>';
                }
                
                return $actions;
            })
            ->editColumn('name', function ($row) {
                $html = '<strong>' . $row->name . '</strong><br>';
                $html .= '<small class="text-muted">' . $row->phone . '</small>';
                return $html;
            })
            ->editColumn('license_info', function ($row) {
                $html = $row->license_number . '<br>';
                $expiryClass = $row->isLicenseExpired() ? 'text-danger' : ($row->isLicenseExpiringSoon() ? 'text-warning' : 'text-success');
                $html .= '<small class="' . $expiryClass . '">Expires: ' . $row->license_expiry->format('Y-m-d') . '</small>';
                return $html;
            })
            ->editColumn('vehicle_info', function ($row) {
                return $row->vehicle_info;
            })
            ->editColumn('status', function ($row) {
                $badges = [
                    'active' => 'bg-success',
                    'inactive' => 'bg-secondary',
                    'suspended' => 'bg-danger'
                ];
                $badgeClass = $badges[$row->status] ?? 'bg-secondary';
                return '<span class="badge ' . $badgeClass . '">' . ucfirst($row->status) . '</span>';
            })
            ->addColumn('current_route', function ($row) {
                $route = $row->currentRoute();
                return $route ? $route->name : '<span class="text-muted">No route assigned</span>';
            })
            ->editColumn('salary', function ($row) {
                return $row->salary ? '$' . number_format($row->salary, 2) : 'N/A';
            })
            ->editColumn('hire_date', function ($row) {
                return $row->hire_date->format('Y-m-d');
            })
            ->rawColumns(['actions', 'name', 'license_info', 'status', 'current_route']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Driver $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Driver $model)
    {
        return $model->newQuery()
            ->with(['activeRoutes'])
            ->select('drivers.*');
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('admin-drivers-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(0, 'desc')
            ->buttons(
                Button::make('create')
                    ->text('<i class="fas fa-plus"></i> Add Driver')
                    ->className('btn btn-primary')
                    ->action('window.location.href = "' . route('admin.drivers.create') . '"'),
                Button::make('export'),
                Button::make('print'),
                Button::make('reload')
            )
            ->language($this->getLang());
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->title('ID')->width(60),
            Column::make('name')->title('Driver Info')->searchable(true)->orderable(false),
            Column::make('license_info')->title('License')->orderable(false)->searchable(false),
            Column::make('vehicle_info')->title('Vehicle')->orderable(false)->searchable(false),
            Column::make('current_route')->title('Current Route')->orderable(false)->searchable(false),
            Column::make('salary')->title('Salary')->searchable(false),
            Column::make('status')->title('Status')->searchable(false),
            Column::make('hire_date')->title('Hire Date'),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->width(150)
                ->addClass('text-center')
                ->title('Actions'),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'AdminDrivers_' . date('YmdHis');
    }

    /**
     * Get language file for DataTable
     */
    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }
}
