<?php

return [
    'client_form'                => 'Client Registration Form',
    'client_list'                => 'Client List',
    'call_center'                => 'Customer Service Center',
    'form_has_errors'            => 'There are errors in your submission!',
    'please_check_fields'        => 'Please check the highlighted fields and correct them.',
    'fields_marked_required'     => 'Fields marked with * are required',
    'student_name'               => 'Student Name',
    'parent_information'         => 'Parent Information',
    'father_job'                 => 'Job of Guardian (Father)',
    'mother_job'                 => 'Job of Guardian (Mother)',
    'home_phone'                 => 'Home Phone Number',
    'mother_phone'               => 'Mother\'s Phone Number',
    'father_phone'               => 'Father\'s Phone Number',
    'extra_phone'                => 'Additional Phone Number',
    'contact_priority'           => 'Communication Priority',
    'contact_father'             => 'Father',
    'contact_mother'             => 'Mother',
    'address'                    => 'Detailed Address',
    'location'                   => 'Location',
    'area'                       => 'Area',
    'school'                     => 'School',
    'education_department'       => 'Educational Department',
    'national'                   => 'National',
    'international'              => 'Inter National',
    'igsec'                      => 'IGSEC',
    'french'                     => 'French',
    'american'                   => 'American Diploma',
    'dutch'                      => 'Special Cases',
    'education_stage'            => 'Educational Level',
    'class_level'                => 'Class Level',
    'kg'                         => 'KG',
    'middle'                     => 'Middle',
    'junior'                     => 'Junior',
    'senior'                     => 'Senior',
    'primary'                    => 'Primary',
    'secondary'                  => 'Secondary',
    'preparatory'                => 'Preparatory',
    'entry_time'                 => 'Entry Time',
    'exit_time'                  => 'Check Out Time',
    'car_type'                   => 'Type of Car',
    'qasrawy'                    => 'Qasrawy',
    'high_roof'                  => 'High Roof',
    'private7'                   => 'Private (7 Seats)',
    'private_classic'            => 'Private Car',
    'coaster'                    => 'Coaster (21 Seats)',
    'chevrolet'                  => 'Chevrolet',
    'study_start_date'           => 'Study Start Date',
    'subscription_type'          => 'Subscription Type',
    'subscription_one_time'      => 'Annual Subscription (One Payment)',
    'subscription_monthly'       => 'Annual Subscription (Monthly Installments)',
    'client_type'                => 'Client Type',
    'new_client'                 => 'New Client',
    'old_client'                 => 'Previous Client',
    'comments'                   => 'Comments',
    'submit'                     => 'Submit',
    'created_at'                 => 'Created At',
    'form_received_successfully' => 'Form submitted successfully!',
    'back_to_form'               => 'Register New Client',
    'students'                   => 'Students',
    'add_student'                => 'Add Student',
    'remove_student'             => 'Remove Student',
    'student_number'             => 'Student #',
    'multiple_students_info'     => 'You can add multiple students in the same form',
    'user_account_created'       => 'User account created successfully',
    'default_password_info'      => 'A user account has been created for you with a default password. Account details will be sent via phone.',
    'other_school'               => 'Other School',
    'client_registration'        => 'Client Registration',
    'royal_transit'              => 'Royal Transit',
    'english'                    => 'English',
    'arabic'                     => 'العربية',
    'geolocation_not_supported'  => 'Geolocation is not supported in this browser.',
    'location_access_denied'     => 'Could not get location. Please make sure you allowed location access.',

    // Admin Panel Translations
    'admin_panel'                => 'Admin Panel',
    'dashboard'                  => 'Dashboard',
    'clients'                    => 'Clients',
    'routes'                     => 'Routes',
    'drivers'                    => 'Drivers',
    'financial'                  => 'Financial',
    'reports'                    => 'Reports',
    'settings'                   => 'Settings',
    'logout'                     => 'Logout',

    // Client Management
    'client_management'          => 'Client Management',
    'add_client'                 => 'Add Client',
    'edit_client'                => 'Edit Client',
    'view_client'                => 'View Client',
    'delete_client'              => 'Delete Client',
    'client_details'             => 'Client Details',
    'total_clients'              => 'Total Clients',
    'active_clients'             => 'Active Clients',
    'pending_clients'            => 'Pending Clients',

    // Route Management
    'route_management'           => 'Route Management',
    'add_route'                  => 'Add Route',
    'edit_route'                 => 'Edit Route',
    'view_route'                 => 'View Route',
    'delete_route'               => 'Delete Route',
    'route_details'              => 'Route Details',
    'total_routes'               => 'Total Routes',
    'active_routes'              => 'Active Routes',
    'route_capacity'             => 'Route Capacity',
    'from_area'                  => 'From Area',
    'to_school'                  => 'To School',
    'pickup_time'                => 'Pickup Time',
    'dropoff_time'               => 'Dropoff Time',
    'current_students'           => 'Current Students',
    'capacity'                   => 'Capacity',
    'route_stops'                => 'Route Stops',
    'add_stop'                   => 'Add Stop',
    'remove_stop'                => 'Remove Stop',
    'stop_name'                  => 'Stop Name',
    'stop_time'                  => 'Stop Time',
    'assigned_driver'            => 'Assigned Driver',
    'no_driver_assigned'         => 'No Driver Assigned',

    // Driver Management
    'driver_management'          => 'Driver Management',
    'add_driver'                 => 'Add Driver',
    'edit_driver'                => 'Edit Driver',
    'view_driver'                => 'View Driver',
    'delete_driver'              => 'Delete Driver',
    'driver_details'             => 'Driver Details',
    'total_drivers'              => 'Total Drivers',
    'active_drivers'             => 'Active Drivers',
    'assigned_drivers'           => 'Assigned to Routes',
    'expiring_licenses'          => 'Expiring Licenses',
    'driver_name'                => 'Driver Name',
    'phone_number'               => 'Phone Number',
    'email_address'              => 'Email Address',
    'national_id'                => 'National ID',
    'license_number'             => 'License Number',
    'license_expiry'             => 'License Expiry',
    'hire_date'                  => 'Hire Date',
    'monthly_salary'             => 'Monthly Salary',
    'driver_status'              => 'Driver Status',
    'active'                     => 'Active',
    'inactive'                   => 'Inactive',
    'suspended'                  => 'Suspended',
    'vehicle_information'        => 'Vehicle Information',
    'vehicle_type'               => 'Vehicle Type',
    'vehicle_capacity'           => 'Vehicle Capacity',
    'vehicle_model'              => 'Vehicle Model',
    'vehicle_year'               => 'Vehicle Year',
    'plate_number'               => 'Plate Number',
    'vehicle_color'              => 'Vehicle Color',
    'vehicle_notes'              => 'Vehicle Notes',
    'emergency_contact'          => 'Emergency Contact',
    'contact_name'               => 'Contact Name',
    'contact_phone'              => 'Contact Phone',
    'relationship'               => 'Relationship',
    'personal_information'       => 'Personal Information',
    'license_employment'         => 'License & Employment',
    'additional_notes'           => 'Additional Notes',
    'current_assignment'         => 'Current Assignment',
    'route_assignment_history'   => 'Route Assignment History',
    'driver_statistics'          => 'Driver Statistics',
    'years_of_service'           => 'Years of Service',
    'license_status'             => 'License Status',
    'valid'                      => 'Valid',
    'expired'                    => 'Expired',
    'expiring_soon'              => 'Expiring Soon',
    'suspend_driver'             => 'Suspend Driver',
    'activate_driver'            => 'Activate Driver',
    'remove_from_route'          => 'Remove from Route',
    'assign_to_route'            => 'Assign to Route',

    // Financial Management
    'financial_management'       => 'Financial Management',
    'total_revenue'              => 'Total Revenue',
    'monthly_revenue'            => 'Monthly Revenue',
    'pending_payments'           => 'Pending Payments',
    'payment_history'            => 'Payment History',
    'subscription_revenue'       => 'Subscription Revenue',
    'payment_status'             => 'Payment Status',
    'paid'                       => 'Paid',
    'pending'                    => 'Pending',
    'overdue'                    => 'Overdue',

    // Common Actions
    'actions'                    => 'Actions',
    'create'                     => 'Create',
    'edit'                       => 'Edit',
    'view'                       => 'View',
    'delete'                     => 'Delete',
    'save'                       => 'Save',
    'cancel'                     => 'Cancel',
    'update'                     => 'Update',
    'back'                       => 'Back',
    'search'                     => 'Search',
    'filter'                     => 'Filter',
    'export'                     => 'Export',
    'import'                     => 'Import',
    'refresh'                    => 'Refresh',
    'loading'                    => 'Loading...',
    'no_data'                    => 'No data available',
    'confirm_delete'             => 'Are you sure you want to delete this item?',
    'success'                    => 'Success',
    'error'                      => 'Error',
    'warning'                    => 'Warning',
    'info'                       => 'Information',

    // Form Validation
    'required_field'             => 'This field is required',
    'invalid_email'              => 'Please enter a valid email address',
    'invalid_phone'              => 'Please enter a valid phone number',
    'invalid_date'               => 'Please enter a valid date',
    'min_length'                 => 'Minimum length is :min characters',
    'max_length'                 => 'Maximum length is :max characters',

    // Status Messages
    'created_successfully'       => 'Created successfully',
    'updated_successfully'       => 'Updated successfully',
    'deleted_successfully'       => 'Deleted successfully',
    'operation_failed'           => 'Operation failed',
    'permission_denied'          => 'Permission denied',
    'not_found'                  => 'Item not found',

];
