<!doctype html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('client_registration') }} - {{ __('royal_transit') }}</title>
    <link href="//cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="//fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .brand-header {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #1e3a8a 0%, #fbbf24 100%);
            color: #fff;
        }

        .brand-header h1 {
            font-size: 24px;
            margin: 0;
        }

        .brand-header p {
            margin: 0;
            font-size: 14px;
        }

        .brand-header .logo {
            max-width: 80px;
            height: auto;
            margin-bottom: 10px;
        }

        .contact-links a {
            color: #fff;
            text-decoration: underline;
        }

        .lang-switcher {
            position: fixed;
            top: 20px;
            {{ app()->getLocale() == 'ar' ? 'left' : 'right' }}: 20px;
            z-index: 1000;
        }
    </style>
</head>

<body>
    <!-- Language Switcher -->
    <div class="lang-switcher">
        @if (app()->getLocale() == 'ar')
            <a class="btn btn-outline-dark btn-sm" href="{{ url()->current() }}?lang=en">
                    <i class="fas fa-globe"></i> {{ __('english') }}
            </a>
        @else
            <a class="btn btn-outline-dark btn-sm" href="{{ url()->current() }}?lang=ar">
                <i class="fas fa-globe"></i> {{ __('arabic') }}
            </a>
        @endif
    </div>

    <div class="container mt-4">
        <!-- الهيدر -->
        <div class="brand-header d-flex justify-content-between align-items-center p-3 rounded">
            <div class="fw-bold fs-4 text-center">
                <img src="/images/logo.png" alt="Royal Transit Logo" class="logo d-block mx-auto mb-2">
                {{ __('royal_transit') }}
            </div>
            <div class="text-end">
                <p class="mb-1">{{ __('messages.call_center') }}:</p>
                <p class="mb-1" dir="ltr">
                    <a class="text-white text-decoration-underline" href="tel:@lang('phone1')">@lang('phone1')</a>
                </p>
                <p class="mb-0" dir="ltr">
                    <a class="text-white text-decoration-underline" href="tel:@lang('phone2')">@lang('phone2')</a>
                </p>
            </div>
        </div>

        <h2 class="mb-4 text-center">{{ __('client_registration') }}</h2>

        @if (session('success'))
            <div class="alert alert-success text-center">
                {{ session('success') }}
            </div>
        @endif

        @if ($errors->any())
            <div class="alert alert-danger">
                <strong>{{ __('messages.form_has_errors') }}</strong>
                <br>
                {{ __('messages.please_check_fields') }}
            </div>
        @endif


        {{-- FORM PLACEHOLDER --}}
        @include('partials.client_form_fields')

    </div>
    <script>
        function toggleOtherSchoolStudent(select, studentIndex) {
            const otherDiv = document.getElementById('otherSchoolStudentDiv' + studentIndex);
            if (select.value === 'other') {
                otherDiv.style.display = 'block';
            } else {
                otherDiv.style.display = 'none';
                const otherInput = otherDiv.querySelector('input');
                if (otherInput) {
                    otherInput.value = '';
                }
            }
        }
    </script>
    <script>
        let studentIndex = 1; // Start from 1 since we have student 0 by default

        // Recreate additional students on page load if there are old values (validation errors)
        window.addEventListener('DOMContentLoaded', function() {
            @if(old('students'))
                @php
                    $oldStudents = old('students');
                    $studentCount = count($oldStudents);
                @endphp

                // If there are more than 1 student, recreate the additional ones
                @if($studentCount > 1)
                    @for($i = 1; $i < $studentCount; $i++)
                        addStudentWithData({{ $i }}, {
                            name: '{{ old("students.{$i}.name") }}',
                            school: '{{ old("students.{$i}.school") }}',
                            other_school: '{{ old("students.{$i}.other_school") }}',
                            education_department: '{{ old("students.{$i}.education_department") }}',
                            education_stage: '{{ old("students.{$i}.education_stage") }}',
                            class_level: '{{ old("students.{$i}.class_level") }}',
                            entry_time: '{{ old("students.{$i}.entry_time") }}',
                            exit_time: '{{ old("students.{$i}.exit_time") }}',
                            errors: {
                                name: '{{ $errors->first("students.{$i}.name") }}',
                                school: '{{ $errors->first("students.{$i}.school") }}',
                                other_school: '{{ $errors->first("students.{$i}.other_school") }}',
                                education_department: '{{ $errors->first("students.{$i}.education_department") }}',
                                education_stage: '{{ $errors->first("students.{$i}.education_stage") }}',
                                class_level: '{{ $errors->first("students.{$i}.class_level") }}',
                                entry_time: '{{ $errors->first("students.{$i}.entry_time") }}',
                                exit_time: '{{ $errors->first("students.{$i}.exit_time") }}'
                            }
                        });
                    @endfor
                    studentIndex = {{ $studentCount }};
                @endif
            @endif
        });

        function addStudent() {
            const container = document.getElementById('studentsContainer');
            const studentForm = createStudentForm(studentIndex);
            container.appendChild(studentForm);
            studentIndex++;
        }

        function addStudentWithData(index, data) {
            const container = document.getElementById('studentsContainer');
            const studentForm = createStudentForm(index, data);
            container.appendChild(studentForm);
        }

        function removeStudent(index) {
            const studentForm = document.querySelector(`[data-student-index="${index}"]`);
            if (studentForm) {
                studentForm.remove();
            }
        }

        function createStudentForm(index, data = null) {
            const div = document.createElement('div');
            div.className = 'student-form border rounded p-3 mb-3';
            div.setAttribute('data-student-index', index);

            div.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">{{ __('messages.student_number') }} ${index + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeStudent(${index})">
                        <i class="fas fa-trash"></i> {{ __('messages.remove_student') }}
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label class="form-label">{{ __('messages.student_name') }} <span class="text-danger">*</span></label>
                        <input type="text" name="students[${index}][name]" class="form-control ${data && data.errors && data.errors.name ? 'is-invalid' : ''}" value="${data ? data.name : ''}" required>
                        ${data && data.errors && data.errors.name ? `<div class="invalid-feedback">${data.errors.name}</div>` : ''}
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">{{ __('messages.school') }} <span class="text-danger">*</span></label>
                        <select name="students[${index}][school]" class="form-select ${data && data.errors && data.errors.school ? 'is-invalid' : ''}" onchange="toggleOtherSchoolStudent(this, ${index})" required>
                            @foreach(__('schools') as $key => $value)
                                <option value="{{ $key }}" ${data && data.school === '{{ $key }}' ? 'selected' : ''}>{{ $value }}</option>
                            @endforeach
                        </select>
                        ${data && data.errors && data.errors.school ? `<div class="invalid-feedback">${data.errors.school}</div>` : ''}
                    </div>
                    <div class="col-md-3 mb-3" id="otherSchoolStudentDiv${index}" style="display: ${data && data.school === 'other' ? 'block' : 'none'};">
                        <label class="form-label">{{ __('messages.other_school') }} <span class="text-danger">*</span></label>
                        <input type="text" name="students[${index}][other_school]" class="form-control ${data && data.errors && data.errors.other_school ? 'is-invalid' : ''}" value="${data ? data.other_school : ''}">
                        ${data && data.errors && data.errors.other_school ? `<div class="invalid-feedback">${data.errors.other_school}</div>` : ''}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-2 mb-3">
                        <label class="form-label">{{ __('messages.education_department') }} <span class="text-danger">*</span></label>
                        <select class="form-select ${data && data.errors && data.errors.education_department ? 'is-invalid' : ''}" name="students[${index}][education_department]" required>
                            <option value="national" ${data && data.education_department === 'national' ? 'selected' : ''}>{{ __('messages.national') }}</option>
                            <option value="international" ${data && data.education_department === 'international' ? 'selected' : ''}>{{ __('messages.international') }}</option>
                            <option value="igsec" ${data && data.education_department === 'igsec' ? 'selected' : ''}>{{ __('messages.igsec') }}</option>
                            <option value="french" ${data && data.education_department === 'french' ? 'selected' : ''}>{{ __('messages.french') }}</option>
                            <option value="american" ${data && data.education_department === 'american' ? 'selected' : ''}>{{ __('messages.american') }}</option>
                            <option value="dutch" ${data && data.education_department === 'dutch' ? 'selected' : ''}>{{ __('messages.dutch') }}</option>
                        </select>
                        ${data && data.errors && data.errors.education_department ? `<div class="invalid-feedback">${data.errors.education_department}</div>` : ''}
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">{{ __('messages.education_stage') }} <span class="text-danger">*</span></label>
                        <select class="form-select ${data && data.errors && data.errors.education_stage ? 'is-invalid' : ''}" name="students[${index}][education_stage]" required>
                            <option value="junior" ${data && data.education_stage === 'junior' ? 'selected' : ''}>{{ __('messages.junior') }}</option>
                            <option value="kg" ${data && data.education_stage === 'kg' ? 'selected' : ''}>{{ __('messages.kg') }}</option>
                            <option value="primary" ${data && data.education_stage === 'primary' ? 'selected' : ''}>{{ __('messages.primary') }}</option>
                            <option value="preparatory" ${data && data.education_stage === 'preparatory' ? 'selected' : ''}>{{ __('messages.preparatory') }}</option>
                            <option value="secondary" ${data && data.education_stage === 'secondary' ? 'selected' : ''}>{{ __('messages.secondary') }}</option>
                        </select>
                        ${data && data.errors && data.errors.education_stage ? `<div class="invalid-feedback">${data.errors.education_stage}</div>` : ''}
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">{{ __('messages.class_level') }} <span class="text-danger">*</span></label>
                        <input type="text" name="students[${index}][class_level]" class="form-control ${data && data.errors && data.errors.class_level ? 'is-invalid' : ''}" value="${data ? data.class_level : ''}" required>
                        ${data && data.errors && data.errors.class_level ? `<div class="invalid-feedback">${data.errors.class_level}</div>` : ''}
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">{{ __('messages.entry_time') }} <span class="text-danger">*</span></label>
                        <input type="time" name="students[${index}][entry_time]" class="form-control ${data && data.errors && data.errors.entry_time ? 'is-invalid' : ''}" value="${data ? data.entry_time : ''}" required>
                        ${data && data.errors && data.errors.entry_time ? `<div class="invalid-feedback">${data.errors.entry_time}</div>` : ''}
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">{{ __('messages.exit_time') }} <span class="text-danger">*</span></label>
                        <input type="time" name="students[${index}][exit_time]" class="form-control ${data && data.errors && data.errors.exit_time ? 'is-invalid' : ''}" value="${data ? data.exit_time : ''}" required>
                        ${data && data.errors && data.errors.exit_time ? `<div class="invalid-feedback">${data.errors.exit_time}</div>` : ''}
                    </div>
                </div>
            `;

            return div;
        }

        function getLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(successCallback, errorCallback);
            } else {
                alert("{{ __('messages.geolocation_not_supported') }}");
            }
        }

        function successCallback(position) {
            const latitude = position.coords.latitude;
            const longitude = position.coords.longitude;
            document.getElementById('locationInput').value = `${latitude}, ${longitude}`;
        }

        function errorCallback(error) {
            alert("{{ __('messages.location_access_denied') }}");
        }
    </script>

</body>

</html>
