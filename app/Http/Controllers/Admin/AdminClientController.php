<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\User;
use App\Models\Student;
use App\DataTables\AdminClientsDataTable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class AdminClientController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:read admin_clients'])->only(['index', 'show']);
        $this->middleware(['permission:create admin_clients'])->only(['create', 'store']);
        $this->middleware(['permission:update admin_clients'])->only(['edit', 'update']);
        $this->middleware(['permission:delete admin_clients'])->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(AdminClientsDataTable $dataTable)
    {
        return $dataTable->render('admin.clients.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.clients.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'required|string|max:20',
            'password' => 'required|string|min:8',
            'father_job' => 'nullable|string|max:255',
            'mother_job' => 'nullable|string|max:255',
            'home_phone' => 'nullable|string|max:20',
            'mother_phone' => 'nullable|string|max:20',
            'father_phone' => 'nullable|string|max:20',
            'extra_phone' => 'nullable|string|max:20',
            'contact_priority' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'area' => 'nullable|string|max:255',
            'car_type' => 'nullable|string|max:255',
            'study_start_date' => 'nullable|date',
            'subscription_type' => 'nullable|string|max:255',
            'client_type' => 'nullable|string|max:255',
            'comments' => 'nullable|string',
        ]);

        DB::transaction(function () use ($request) {
            // Create user account
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'user_type_id' => 2, // Assuming 2 is for clients
            ]);

            // Create client profile
            Client::create([
                'user_id' => $user->id,
                'father_job' => $request->father_job,
                'mother_job' => $request->mother_job,
                'home_phone' => $request->home_phone,
                'mother_phone' => $request->mother_phone,
                'father_phone' => $request->father_phone,
                'extra_phone' => $request->extra_phone,
                'contact_priority' => $request->contact_priority,
                'address' => $request->address,
                'location' => $request->location,
                'area' => $request->area,
                'car_type' => $request->car_type,
                'study_start_date' => $request->study_start_date,
                'subscription_type' => $request->subscription_type,
                'client_type' => $request->client_type,
                'comments' => $request->comments,
            ]);
        });

        return redirect()->route('admin.clients.index')
            ->with('success', 'Client created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Client $client)
    {
        $client->load(['user', 'students', 'subscriptions.route', 'subscriptions.payments']);
        
        return view('admin.clients.show', compact('client'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Client $client)
    {
        $client->load('user');
        
        return view('admin.clients.edit', compact('client'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Client $client)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $client->user_id,
            'phone' => 'required|string|max:20',
            'father_job' => 'nullable|string|max:255',
            'mother_job' => 'nullable|string|max:255',
            'home_phone' => 'nullable|string|max:20',
            'mother_phone' => 'nullable|string|max:20',
            'father_phone' => 'nullable|string|max:20',
            'extra_phone' => 'nullable|string|max:20',
            'contact_priority' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'area' => 'nullable|string|max:255',
            'car_type' => 'nullable|string|max:255',
            'study_start_date' => 'nullable|date',
            'subscription_type' => 'nullable|string|max:255',
            'client_type' => 'nullable|string|max:255',
            'comments' => 'nullable|string',
        ]);

        DB::transaction(function () use ($request, $client) {
            // Update user account
            $client->user->update([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
            ]);

            // Update client profile
            $client->update([
                'father_job' => $request->father_job,
                'mother_job' => $request->mother_job,
                'home_phone' => $request->home_phone,
                'mother_phone' => $request->mother_phone,
                'father_phone' => $request->father_phone,
                'extra_phone' => $request->extra_phone,
                'contact_priority' => $request->contact_priority,
                'address' => $request->address,
                'location' => $request->location,
                'area' => $request->area,
                'car_type' => $request->car_type,
                'study_start_date' => $request->study_start_date,
                'subscription_type' => $request->subscription_type,
                'client_type' => $request->client_type,
                'comments' => $request->comments,
            ]);
        });

        return redirect()->route('admin.clients.index')
            ->with('success', 'Client updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Client $client)
    {
        DB::transaction(function () use ($client) {
            // Delete related students
            $client->students()->delete();
            
            // Delete client
            $client->delete();
            
            // Optionally delete user account (be careful with this)
            // $client->user->delete();
        });

        return redirect()->route('admin.clients.index')
            ->with('success', 'Client deleted successfully.');
    }
}
