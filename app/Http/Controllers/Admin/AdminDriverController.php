<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\TransportRoute;
use App\DataTables\AdminDriversDataTable;
use App\Traits\AdminAuditLogging;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminDriverController extends Controller
{
    use AdminAuditLogging;

    public function __construct()
    {
        $this->middleware(['permission:read admin_drivers'])->only(['index', 'show']);
        $this->middleware(['permission:create admin_drivers'])->only(['create', 'store']);
        $this->middleware(['permission:update admin_drivers'])->only(['edit', 'update']);
        $this->middleware(['permission:delete admin_drivers'])->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(AdminDriversDataTable $dataTable)
    {
        $this->logAdminAction('viewed', null, [], request());

        return $dataTable->render('admin.drivers.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.drivers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:drivers,phone',
            'email' => 'nullable|email|unique:drivers,email',
            'license_number' => 'required|string|max:255|unique:drivers,license_number',
            'license_expiry' => 'required|date|after:today',
            'national_id' => 'required|string|max:255|unique:drivers,national_id',
            'address' => 'nullable|string',
            'hire_date' => 'required|date',
            'salary' => 'nullable|numeric|min:0',
            'status' => 'required|in:active,inactive,suspended',
            'vehicle_type' => 'nullable|string|max:255',
            'vehicle_model' => 'nullable|string|max:255',
            'vehicle_year' => 'nullable|string|max:4',
            'vehicle_plate_number' => 'nullable|string|max:255',
            'vehicle_color' => 'nullable|string|max:255',
            'vehicle_capacity' => 'nullable|integer|min:1',
            'vehicle_notes' => 'nullable|string',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'emergency_contact_relation' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $driver = Driver::create($request->all());

        // Log the creation
        $this->logAdminAction('created', $driver, [], $request);

        return redirect()->route('admin.drivers.index')
            ->with('success', 'Driver created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Driver $driver)
    {
        $driver->load(['routes', 'activeRoutes']);

        // Log the view action
        $this->logAdminAction('viewed', $driver, [], request());

        return view('admin.drivers.show', compact('driver'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Driver $driver)
    {
        return view('admin.drivers.edit', compact('driver'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Driver $driver)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:drivers,phone,' . $driver->id,
            'email' => 'nullable|email|unique:drivers,email,' . $driver->id,
            'license_number' => 'required|string|max:255|unique:drivers,license_number,' . $driver->id,
            'license_expiry' => 'required|date|after:today',
            'national_id' => 'required|string|max:255|unique:drivers,national_id,' . $driver->id,
            'address' => 'nullable|string',
            'hire_date' => 'required|date',
            'salary' => 'nullable|numeric|min:0',
            'status' => 'required|in:active,inactive,suspended',
            'vehicle_type' => 'nullable|string|max:255',
            'vehicle_model' => 'nullable|string|max:255',
            'vehicle_year' => 'nullable|string|max:4',
            'vehicle_plate_number' => 'nullable|string|max:255',
            'vehicle_color' => 'nullable|string|max:255',
            'vehicle_capacity' => 'nullable|integer|min:1',
            'vehicle_notes' => 'nullable|string',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'emergency_contact_relation' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $driver->update($request->all());

        return redirect()->route('admin.drivers.index')
            ->with('success', 'Driver updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Driver $driver)
    {
        if ($driver->hasActiveRoute()) {
            return redirect()->route('admin.drivers.index')
                ->with('error', 'Cannot delete driver with active route assignments.');
        }

        DB::transaction(function () use ($driver) {
            // End all route assignments
            $driver->routes()->updateExistingPivot(
                $driver->routes()->pluck('routes.id')->toArray(),
                ['status' => 'inactive', 'end_date' => now()]
            );
            
            $driver->delete();
        });

        return redirect()->route('admin.drivers.index')
            ->with('success', 'Driver deleted successfully.');
    }

    /**
     * Assign driver to route
     */
    public function assignRoute(Request $request, Driver $driver)
    {
        $request->validate([
            'route_id' => 'required|exists:routes,id',
        ]);

        if ($driver->hasActiveRoute()) {
            return redirect()->back()
                ->with('error', 'Driver already has an active route assignment.');
        }

        $route = TransportRoute::find($request->route_id);
        
        if ($route->currentDriver()) {
            return redirect()->back()
                ->with('error', 'Route already has an assigned driver.');
        }

        $driver->routes()->attach($request->route_id, [
            'assigned_date' => now(),
            'status' => 'active',
        ]);

        return redirect()->back()
            ->with('success', 'Driver assigned to route successfully.');
    }

    /**
     * Remove driver from route
     */
    public function removeRoute(Driver $driver, TransportRoute $route)
    {
        $driver->routes()->updateExistingPivot($route->id, [
            'status' => 'inactive',
            'end_date' => now(),
        ]);

        return redirect()->back()
            ->with('success', 'Driver removed from route.');
    }

    /**
     * Get drivers with expiring licenses
     */
    public function expiringLicenses()
    {
        $drivers = Driver::withExpiringLicense(30)
            ->active()
            ->orderBy('license_expiry')
            ->get();

        return view('admin.drivers.expiring-licenses', compact('drivers'));
    }

    /**
     * Suspend driver
     */
    public function suspend(Driver $driver)
    {
        $driver->update(['status' => 'suspended']);

        // End active route assignments
        $driver->routes()->updateExistingPivot(
            $driver->activeRoutes()->pluck('routes.id')->toArray(),
            ['status' => 'inactive', 'end_date' => now()]
        );

        return redirect()->back()
            ->with('success', 'Driver suspended successfully.');
    }

    /**
     * Activate driver
     */
    public function activate(Driver $driver)
    {
        $driver->update(['status' => 'active']);

        return redirect()->back()
            ->with('success', 'Driver activated successfully.');
    }
}
