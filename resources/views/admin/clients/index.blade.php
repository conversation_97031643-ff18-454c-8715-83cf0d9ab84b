@extends('layouts.admin')

@php
    $pageTitle = __('messages.client_management');
    $pageActions = '<a href="' . route('admin.clients.create') . '" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>' . __('messages.add_client') . '
                    </a>';
@endphp

@section('content')
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ __('messages.total_clients') }}</h6>
                        <h3 class="mb-0">{{ \App\Models\Client::count() }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ __('messages.active_clients') }}</h6>
                        <h3 class="mb-0">{{ \App\Models\Subscription::where('status', 'active')->count() }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ __('messages.students') }}</h6>
                        <h3 class="mb-0">{{ \App\Models\Student::count() }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-graduation-cap fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ __('New This Month') }}</h6>
                        <h3 class="mb-0">{{ \App\Models\Client::whereMonth('created_at', now()->month)->count() }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-plus fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- DataTable -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>{{ __('messages.client_list') }}
        </h5>
    </div>
    <div class="card-body">
        {!! $dataTable->table(['class' => 'table table-striped table-hover']) !!}
    </div>
</div>
@endsection

@push('scripts')
{!! $dataTable->scripts() !!}
@endpush
