@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar-custom sidebar">
            <div class="position-sticky pt-3">
                <div class="text-center mb-4">
                    <img src="/images/logo.png" alt="Royal Transit Logo" style="max-width: 90px; height: auto; margin-bottom: 10px;">
                    <h5 class="mt-2">Royal Transit</h5>
                    <p class="text-muted small">Admin Dashboard</p>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    @can('read admin_clients')
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('admin.clients.index') }}">
                            <i class="fas fa-users me-2"></i>Clients
                        </a>
                    </li>
                    @endcan
                    @can('read admin_routes')
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.routes.index') }}">
                            <i class="fas fa-route me-2"></i>Routes
                        </a>
                    </li>
                    @endcan
                    @can('read admin_drivers')
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.drivers.index') }}">
                            <i class="fas fa-car me-2"></i>Drivers
                        </a>
                    </li>
                    @endcan
                    @can('read admin_financial_reports')
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.financial.index') }}">
                            <i class="fas fa-chart-bar me-2"></i>Financial
                        </a>
                    </li>
                    @endcan
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.subscriptions.index') }}">
                            <i class="fas fa-clipboard-list me-2"></i>Subscriptions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.payments.index') }}">
                            <i class="fas fa-credit-card me-2"></i>Payments
                        </a>
                    </li>
                </ul>
                
                <hr>
                
                <div class="dropdown">
                    <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" 
                       id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle me-2"></i>
                        <strong>{{ Auth::user()->name }}</strong>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark text-small shadow" aria-labelledby="dropdownUser1">
                        <li><a class="dropdown-item" href="{{ route('profile.show') }}">Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item">Sign out</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-user me-2"></i>Client Details: {{ $client->user->name }}
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ route('admin.clients.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Clients
                    </a>
                    @can('update admin_clients')
                    <a href="{{ route('admin.clients.edit', $client) }}" class="btn btn-main">
                        <i class="fas fa-edit me-1"></i>Edit Client
                    </a>
                    @endcan
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>Basic Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Full Name:</label>
                                        <p class="mb-0">{{ $client->user->name }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Email Address:</label>
                                        <p class="mb-0">{{ $client->user->email }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Phone Number:</label>
                                        <p class="mb-0">{{ $client->user->phone }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Member Since:</label>
                                        <p class="mb-0">{{ $client->user->created_at->format('F j, Y') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-phone me-2"></i>Contact Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Home Phone:</label>
                                        <p class="mb-0">{{ $client->home_phone ?: 'Not provided' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Mother's Phone:</label>
                                        <p class="mb-0">{{ $client->mother_phone ?: 'Not provided' }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Father's Phone:</label>
                                        <p class="mb-0">{{ $client->father_phone ?: 'Not provided' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Extra Phone:</label>
                                        <p class="mb-0">{{ $client->extra_phone ?: 'Not provided' }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            @if($client->contact_priority)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Contact Priority:</label>
                                <p class="mb-0">{{ $client->contact_priority }}</p>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Address -->
                    @if($client->address)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-map-marker-alt me-2"></i>Address
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">{{ $client->address }}</p>
                        </div>
                    </div>
                    @endif

                    <!-- Comments -->
                    @if($client->comments)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-comment me-2"></i>Comments
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">{{ $client->comments }}</p>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Additional Information -->
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Additional Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Father's Job:</label>
                                <p class="mb-0">{{ $client->father_job ?: 'Not provided' }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Mother's Job:</label>
                                <p class="mb-0">{{ $client->mother_job ?: 'Not provided' }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Area:</label>
                                <p class="mb-0">{{ $client->area ?: 'Not provided' }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Location:</label>
                                <p class="mb-0">{{ $client->location ?: 'Not provided' }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Car Type:</label>
                                <p class="mb-0">{{ $client->car_type ?: 'Not provided' }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Study Start Date:</label>
                                <p class="mb-0">{{ $client->study_start_date ? $client->study_start_date->format('F j, Y') : 'Not provided' }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Subscription Type:</label>
                                <p class="mb-0">
                                    @if($client->subscription_type)
                                        <span class="badge bg-primary">{{ ucfirst($client->subscription_type) }}</span>
                                    @else
                                        Not provided
                                    @endif
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Client Type:</label>
                                <p class="mb-0">
                                    @if($client->client_type)
                                        <span class="badge bg-{{ $client->client_type === 'vip' ? 'warning' : ($client->client_type === 'corporate' ? 'info' : 'secondary') }}">
                                            {{ ucfirst($client->client_type) }}
                                        </span>
                                    @else
                                        Not provided
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>Statistics
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Total Students:</label>
                                <p class="mb-0">{{ $client->students->count() }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Active Subscriptions:</label>
                                <p class="mb-0">{{ $client->subscriptions->where('status', 'active')->count() }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Total Payments:</label>
                                <p class="mb-0">${{ number_format($client->payments->sum('amount'), 2) }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Last Payment:</label>
                                <p class="mb-0">
                                    @if($client->payments->count() > 0)
                                        {{ $client->payments->latest()->first()->created_at->format('F j, Y') }}
                                    @else
                                        No payments yet
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Students List -->
            @if($client->students->count() > 0)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>Students ({{ $client->students->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>School</th>
                                    <th>Grade</th>
                                    <th>Route</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($client->students as $student)
                                <tr>
                                    <td>{{ $student->name }}</td>
                                    <td>{{ $student->school ?: 'Not specified' }}</td>
                                    <td>{{ $student->grade ?: 'Not specified' }}</td>
                                    <td>
                                        @if($student->subscription && $student->subscription->route)
                                            {{ $student->subscription->route->name }}
                                        @else
                                            No route assigned
                                        @endif
                                    </td>
                                    <td>
                                        @if($student->subscription)
                                            <span class="badge bg-{{ $student->subscription->status === 'active' ? 'success' : 'secondary' }}">
                                                {{ ucfirst($student->subscription->status) }}
                                            </span>
                                        @else
                                            <span class="badge bg-warning">No subscription</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif

            <!-- Recent Payments -->
            @if($client->payments->count() > 0)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-credit-card me-2"></i>Recent Payments
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                    <th>Status</th>
                                    <th>Reference</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($client->payments->latest()->take(5) as $payment)
                                <tr>
                                    <td>{{ $payment->created_at->format('M j, Y') }}</td>
                                    <td>${{ number_format($payment->amount, 2) }}</td>
                                    <td>{{ ucfirst($payment->payment_method) }}</td>
                                    <td>
                                        <span class="badge bg-{{ $payment->status === 'completed' ? 'success' : ($payment->status === 'pending' ? 'warning' : 'danger') }}">
                                            {{ ucfirst($payment->status) }}
                                        </span>
                                    </td>
                                    <td>{{ $payment->reference_number ?: 'N/A' }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </main>
    </div>
</div>
@endsection

@push('styles')
<style>
    .sidebar-custom {
        background: linear-gradient(135deg, var(--main-blue) 0%, var(--main-gold) 100%);
        min-height: 100vh;
    }
    
    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        margin-bottom: 0.25rem;
    }
    
    .sidebar .nav-link:hover,
    .sidebar .nav-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .sidebar .nav-link i {
        width: 20px;
    }
    
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        background-color: #f8f9fa;
    }
</style>
@endpush
