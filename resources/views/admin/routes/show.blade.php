@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        @include('admin.partials.sidebar')

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-route me-2"></i>Route Details: {{ $route->name }}
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ route('admin.routes.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Routes
                    </a>
                    @can('update admin_routes')
                    <a href="{{ route('admin.routes.edit', $route) }}" class="btn btn-main">
                        <i class="fas fa-edit me-1"></i>Edit Route
                    </a>
                    @endcan
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <!-- Route Information -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Route Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Route Name:</label>
                                        <p class="mb-0">{{ $route->name }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Status:</label>
                                        <p class="mb-0">
                                            <span class="badge {{ $route->status_badge }}">
                                                {{ ucfirst($route->status) }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">From Area:</label>
                                        <p class="mb-0">{{ $route->from_area }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">To School:</label>
                                        <p class="mb-0">{{ $route->to_school }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Pickup Time:</label>
                                        <p class="mb-0">{{ $route->pickup_time ? $route->pickup_time->format('h:i A') : 'Not set' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Dropoff Time:</label>
                                        <p class="mb-0">{{ $route->dropoff_time ? $route->dropoff_time->format('h:i A') : 'Not set' }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Monthly Price:</label>
                                        <p class="mb-0">SAR {{ number_format($route->monthly_price, 2) }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Created:</label>
                                        <p class="mb-0">{{ $route->created_at->format('F j, Y') }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            @if($route->description)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Description:</label>
                                <p class="mb-0">{{ $route->description }}</p>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Route Stops -->
                    @if($route->stops && count($route->stops) > 0)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-map-marker-alt me-2"></i>Route Stops
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="route-stops">
                                @foreach($route->stops as $index => $stop)
                                <div class="stop-item d-flex align-items-center mb-3">
                                    <div class="stop-number">
                                        <span class="badge bg-primary rounded-circle">{{ $index + 1 }}</span>
                                    </div>
                                    <div class="stop-info ms-3">
                                        <strong>{{ $stop }}</strong>
                                    </div>
                                </div>
                                @if($index < count($route->stops) - 1)
                                <div class="stop-connector">
                                    <i class="fas fa-arrow-down text-muted"></i>
                                </div>
                                @endif
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Current Students -->
                    @if($route->subscriptions->where('status', 'active')->count() > 0)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-graduation-cap me-2"></i>Current Students ({{ $route->subscriptions->where('status', 'active')->count() }})
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Student Name</th>
                                            <th>Client</th>
                                            <th>Subscription Type</th>
                                            <th>Price</th>
                                            <th>Start Date</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($route->subscriptions->where('status', 'active') as $subscription)
                                        <tr>
                                            <td>{{ $subscription->student_name }}</td>
                                            <td>{{ $subscription->client->user->name }}</td>
                                            <td>{{ ucfirst($subscription->subscription_type) }}</td>
                                            <td>SAR {{ number_format($subscription->price, 2) }}</td>
                                            <td>{{ $subscription->start_date->format('M j, Y') }}</td>
                                            <td>
                                                <span class="badge {{ $subscription->status_badge }}">
                                                    {{ ucfirst($subscription->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Statistics & Driver Info -->
                <div class="col-md-4">
                    <!-- Capacity Statistics -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-pie me-2"></i>Capacity Statistics
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Total Capacity:</label>
                                <p class="mb-0">{{ $route->capacity }} students</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Current Students:</label>
                                <p class="mb-0">{{ $route->current_students }} students</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Available Seats:</label>
                                <p class="mb-0">{{ $route->available_seats }} seats</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Capacity Usage:</label>
                                <div class="progress mt-1">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ $route->capacity_percentage }}%"
                                         aria-valuenow="{{ $route->capacity_percentage }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                        {{ number_format($route->capacity_percentage, 1) }}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Driver Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-tie me-2"></i>Driver Information
                            </h5>
                        </div>
                        <div class="card-body">
                            @php $currentDriver = $route->currentDriver(); @endphp
                            @if($currentDriver)
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Driver Name:</label>
                                    <p class="mb-0">{{ $currentDriver->name }}</p>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Phone:</label>
                                    <p class="mb-0">{{ $currentDriver->phone }}</p>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label fw-bold">License Number:</label>
                                    <p class="mb-0">{{ $currentDriver->license_number }}</p>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Vehicle Info:</label>
                                    <p class="mb-0">{{ $currentDriver->vehicle_info }}</p>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Status:</label>
                                    <p class="mb-0">
                                        <span class="badge {{ $currentDriver->status_badge }}">
                                            {{ ucfirst($currentDriver->status) }}
                                        </span>
                                    </p>
                                </div>
                                
                                @can('read admin_drivers')
                                <div class="d-grid">
                                    <a href="{{ route('admin.drivers.show', $currentDriver) }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View Driver Details
                                    </a>
                                </div>
                                @endcan
                            @else
                                <div class="text-center text-muted">
                                    <i class="fas fa-user-slash fa-3x mb-3"></i>
                                    <p>No driver assigned to this route</p>
                                    @can('update admin_routes')
                                    <a href="{{ route('admin.routes.edit', $route) }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-plus me-1"></i>Assign Driver
                                    </a>
                                    @endcan
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Financial Summary -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-dollar-sign me-2"></i>Financial Summary
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Active Subscriptions:</label>
                                <p class="mb-0">{{ $route->subscriptions->where('status', 'active')->count() }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Monthly Revenue:</label>
                                <p class="mb-0">SAR {{ number_format($route->subscriptions->where('status', 'active')->sum('price'), 2) }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Potential Revenue:</label>
                                <p class="mb-0">SAR {{ number_format($route->capacity * $route->monthly_price, 2) }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Revenue Efficiency:</label>
                                @php
                                    $currentRevenue = $route->subscriptions->where('status', 'active')->sum('price');
                                    $potentialRevenue = $route->capacity * $route->monthly_price;
                                    $efficiency = $potentialRevenue > 0 ? ($currentRevenue / $potentialRevenue) * 100 : 0;
                                @endphp
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ $efficiency }}%"
                                         aria-valuenow="{{ $efficiency }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                        {{ number_format($efficiency, 1) }}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
@endsection

@push('styles')
<style>
    .sidebar-custom {
        background: linear-gradient(135deg, var(--main-blue) 0%, var(--main-gold) 100%);
        min-height: 100vh;
    }
    
    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        margin-bottom: 0.25rem;
    }
    
    .sidebar .nav-link:hover,
    .sidebar .nav-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .sidebar .nav-link i {
        width: 20px;
    }
    
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        background-color: #f8f9fa;
    }
    
    .progress {
        height: 20px;
    }
    
    .route-stops .stop-item {
        position: relative;
    }
    
    .route-stops .stop-connector {
        text-align: center;
        margin: 0.5rem 0;
        margin-left: 15px;
    }
    
    .stop-number .badge {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }
</style>
@endpush
