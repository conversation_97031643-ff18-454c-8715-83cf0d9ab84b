<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransportRoute extends Model
{
    use HasFactory;

    protected $table = 'routes';

    protected $fillable = [
        'name',
        'from_area',
        'to_school',
        'pickup_time',
        'dropoff_time',
        'monthly_price',
        'capacity',
        'current_students',
        'status',
        'description',
        'stops'
    ];

    protected $casts = [
        'pickup_time' => 'datetime:H:i',
        'dropoff_time' => 'datetime:H:i',
        'monthly_price' => 'decimal:2',
        'stops' => 'array'
    ];

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'route_id');
    }

    /**
     * Get the drivers assigned to this route
     */
    public function drivers()
    {
        return $this->belongsToMany(Driver::class, 'driver_route', 'route_id', 'driver_id')
                    ->withPivot(['assigned_date', 'end_date', 'status', 'notes'])
                    ->withTimestamps();
    }

    /**
     * Get active drivers for this route
     */
    public function activeDrivers()
    {
        return $this->drivers()->wherePivot('status', 'active');
    }

    /**
     * Get the current active driver for this route
     */
    public function currentDriver()
    {
        return $this->activeDrivers()->first();
    }

    public function getAvailableSeatsAttribute()
    {
        return $this->capacity - $this->current_students;
    }

    public function getCapacityPercentageAttribute()
    {
        return ($this->current_students / $this->capacity) * 100;
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'active' => 'bg-success',
            'inactive' => 'bg-secondary'
        ];

        return $badges[$this->status] ?? 'bg-secondary';
    }
}
