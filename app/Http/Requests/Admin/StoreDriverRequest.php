<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StoreDriverRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->user()->can('create admin_drivers');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            // Personal Information
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:drivers,email|max:255',
            'phone' => 'required|string|max:20|unique:drivers,phone',
            'national_id' => 'required|string|max:20|unique:drivers,national_id',
            'date_of_birth' => 'required|date|before:today',
            'address' => 'required|string|max:500',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',
            
            // License Information
            'license_number' => 'required|string|max:50|unique:drivers,license_number',
            'license_type' => 'required|in:private,commercial,heavy,motorcycle',
            'license_expiry_date' => 'required|date|after:today',
            
            // Employment Information
            'hire_date' => 'required|date|before_or_equal:today',
            'salary' => 'required|numeric|min:0|max:999999.99',
            'status' => 'required|in:active,inactive,suspended,on_leave',
            'shift_type' => 'required|in:morning,evening,full_day,night',
            
            // Vehicle Assignment
            'vehicle_id' => 'nullable|exists:vehicles,id',
            'route_ids' => 'nullable|array',
            'route_ids.*' => 'exists:routes,id',
            
            // Additional Information
            'notes' => 'nullable|string|max:1000',
            'medical_certificate_expiry' => 'nullable|date|after:today',
            'background_check_date' => 'nullable|date|before_or_equal:today',
            'training_completion_date' => 'nullable|date|before_or_equal:today',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => __('messages.required_field'),
            'name.max' => __('messages.max_length', ['max' => 255]),
            'email.required' => __('messages.required_field'),
            'email.email' => __('Please enter a valid email address'),
            'email.unique' => __('Email address already exists'),
            'email.max' => __('messages.max_length', ['max' => 255]),
            'phone.required' => __('messages.required_field'),
            'phone.unique' => __('Phone number already exists'),
            'phone.max' => __('messages.max_length', ['max' => 20]),
            'national_id.required' => __('messages.required_field'),
            'national_id.unique' => __('National ID already exists'),
            'national_id.max' => __('messages.max_length', ['max' => 20]),
            'date_of_birth.required' => __('messages.required_field'),
            'date_of_birth.date' => __('messages.invalid_date'),
            'date_of_birth.before' => __('Date of birth must be before today'),
            'address.required' => __('messages.required_field'),
            'address.max' => __('messages.max_length', ['max' => 500]),
            'emergency_contact_name.required' => __('messages.required_field'),
            'emergency_contact_name.max' => __('messages.max_length', ['max' => 255]),
            'emergency_contact_phone.required' => __('messages.required_field'),
            'emergency_contact_phone.max' => __('messages.max_length', ['max' => 20]),
            
            // License validation messages
            'license_number.required' => __('messages.required_field'),
            'license_number.unique' => __('License number already exists'),
            'license_number.max' => __('messages.max_length', ['max' => 50]),
            'license_type.required' => __('messages.required_field'),
            'license_type.in' => __('Please select a valid license type'),
            'license_expiry_date.required' => __('messages.required_field'),
            'license_expiry_date.date' => __('messages.invalid_date'),
            'license_expiry_date.after' => __('License expiry date must be in the future'),
            
            // Employment validation messages
            'hire_date.required' => __('messages.required_field'),
            'hire_date.date' => __('messages.invalid_date'),
            'hire_date.before_or_equal' => __('Hire date cannot be in the future'),
            'salary.required' => __('messages.required_field'),
            'salary.numeric' => __('Salary must be a valid number'),
            'salary.min' => __('Salary must be at least 0'),
            'salary.max' => __('Salary cannot exceed 999,999.99'),
            'status.required' => __('messages.required_field'),
            'status.in' => __('Please select a valid status'),
            'shift_type.required' => __('messages.required_field'),
            'shift_type.in' => __('Please select a valid shift type'),
            
            // Vehicle and route validation messages
            'vehicle_id.exists' => __('Selected vehicle does not exist'),
            'route_ids.array' => __('Routes must be an array'),
            'route_ids.*.exists' => __('Selected route does not exist'),
            
            // Additional information validation messages
            'notes.max' => __('messages.max_length', ['max' => 1000]),
            'medical_certificate_expiry.date' => __('messages.invalid_date'),
            'medical_certificate_expiry.after' => __('Medical certificate expiry must be in the future'),
            'background_check_date.date' => __('messages.invalid_date'),
            'background_check_date.before_or_equal' => __('Background check date cannot be in the future'),
            'training_completion_date.date' => __('messages.invalid_date'),
            'training_completion_date.before_or_equal' => __('Training completion date cannot be in the future'),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => __('messages.name'),
            'email' => __('messages.email'),
            'phone' => __('messages.phone'),
            'national_id' => __('messages.national_id'),
            'date_of_birth' => __('messages.date_of_birth'),
            'address' => __('messages.address'),
            'emergency_contact_name' => __('messages.emergency_contact_name'),
            'emergency_contact_phone' => __('messages.emergency_contact_phone'),
            'license_number' => __('messages.license_number'),
            'license_type' => __('messages.license_type'),
            'license_expiry_date' => __('messages.license_expiry_date'),
            'hire_date' => __('messages.hire_date'),
            'salary' => __('messages.salary'),
            'status' => __('messages.status'),
            'shift_type' => __('messages.shift_type'),
            'vehicle_id' => __('messages.vehicle'),
            'route_ids' => __('messages.routes'),
            'notes' => __('messages.notes'),
            'medical_certificate_expiry' => __('messages.medical_certificate_expiry'),
            'background_check_date' => __('messages.background_check_date'),
            'training_completion_date' => __('messages.training_completion_date'),
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Clean phone numbers
        $phoneFields = ['phone', 'emergency_contact_phone'];
        foreach ($phoneFields as $field) {
            if ($this->has($field)) {
                $this->merge([
                    $field => preg_replace('/[^0-9+]/', '', $this->input($field))
                ]);
            }
        }

        // Clean national ID
        if ($this->has('national_id')) {
            $this->merge([
                'national_id' => preg_replace('/[^0-9]/', '', $this->input('national_id'))
            ]);
        }

        // Ensure route_ids is an array
        if ($this->has('route_ids') && !is_array($this->input('route_ids'))) {
            $this->merge(['route_ids' => []]);
        }
    }
}
