<!-- Sidebar -->
<nav class="col-md-3 col-lg-2 d-md-block sidebar-custom sidebar">
    <div class="position-sticky pt-3">
        <div class="text-center mb-4">
            <img src="/images/logo.png" alt="{{ __('messages.royal_transit') }} Logo" style="max-width: 90px; height: auto; margin-bottom: 10px;">
            <h5 class="mt-2">{{ __('messages.royal_transit') }}</h5>
            <p class="text-muted small">{{ __('messages.admin_panel') }}</p>
        </div>

        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>{{ __('messages.dashboard') }}
                </a>
            </li>
            @can('view admin_clients')
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.clients.*') ? 'active' : '' }}" href="{{ route('admin.clients.index') }}">
                    <i class="fas fa-users me-2"></i>{{ __('messages.clients') }}
                </a>
            </li>
            @endcan
            @can('view admin_routes')
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.routes.*') ? 'active' : '' }}" href="{{ route('admin.routes.index') }}">
                    <i class="fas fa-route me-2"></i>{{ __('messages.routes') }}
                </a>
            </li>
            @endcan
            @can('view admin_drivers')
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.drivers.*') ? 'active' : '' }}" href="{{ route('admin.drivers.index') }}">
                    <i class="fas fa-car me-2"></i>{{ __('messages.drivers') }}
                </a>
            </li>
            @endcan
            @can('view admin_financial')
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.financial.*') ? 'active' : '' }}" href="{{ route('admin.financial.index') }}">
                    <i class="fas fa-chart-bar me-2"></i>{{ __('messages.financial') }}
                </a>
            </li>
            @endcan
            @can('view admin_subscriptions')
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.subscriptions.*') ? 'active' : '' }}" href="{{ route('admin.subscriptions.index') }}">
                    <i class="fas fa-clipboard-list me-2"></i>{{ __('messages.subscriptions') ?? 'Subscriptions' }}
                </a>
            </li>
            @endcan
            @can('view admin_payments')
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.payments.*') ? 'active' : '' }}" href="{{ route('admin.payments.index') }}">
                    <i class="fas fa-credit-card me-2"></i>{{ __('messages.payments') ?? 'Payments' }}
                </a>
            </li>
            @endcan
        </ul>

        <hr>

        <!-- Language Switcher -->
        <div class="mb-3">
            <div class="dropdown">
                <button class="btn btn-outline-light btn-sm dropdown-toggle w-100" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-globe me-2"></i>
                    {{ app()->getLocale() == 'ar' ? __('messages.arabic') : __('messages.english') }}
                </button>
                <ul class="dropdown-menu w-100" aria-labelledby="languageDropdown">
                    <li>
                        <a class="dropdown-item {{ app()->getLocale() == 'en' ? 'active' : '' }}" href="{{ url()->current() }}?lang=en">
                            <i class="fas fa-flag-usa me-2"></i>{{ __('messages.english') }}
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item {{ app()->getLocale() == 'ar' ? 'active' : '' }}" href="{{ url()->current() }}?lang=ar">
                            <i class="fas fa-flag me-2"></i>{{ __('messages.arabic') }}
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="dropdown">
            <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle"
               id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-user-circle me-2"></i>
                <strong>{{ Auth::user()->name }}</strong>
            </a>
            <ul class="dropdown-menu dropdown-menu-dark text-small shadow" aria-labelledby="dropdownUser1">
                <li><a class="dropdown-item" href="{{ route('profile.show') }}">{{ __('Profile') }}</a></li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="dropdown-item">{{ __('messages.logout') }}</button>
                    </form>
                </li>
            </ul>
        </div>
    </div>
</nav>


