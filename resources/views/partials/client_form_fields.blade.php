<p class="text-danger text-center mb-4">
    <strong>*</strong> {{ __('messages.fields_marked_required') }}
</p>

<form method="POST" action="{{ route('clients.store') }}">
    @csrf

    @php
        $hasError = function ($field) use ($errors) {
            return $errors->has($field) ? 'is-invalid' : '';
        };
    @endphp

    <!-- بيانات ولي الأمر -->
    <div class="card mb-4">
        <div class="card-header bg-dark text-white">{{ __('messages.parent_information') }}</div>
        <div class="card-body row">
            <div class="col-md-6 mb-3">
                <label class="form-label">{{ __('messages.father_job') }} <span class="text-danger">*</span></label>
                <input type="text" name="father_job" class="form-control {{ $hasError('father_job') }}"
                    value="{{ old('father_job') }}" required>
                @error('father_job')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label">{{ __('messages.mother_job') }} <span class="text-danger">*</span></label>
                <input type="text" name="mother_job" class="form-control {{ $hasError('mother_job') }}"
                    value="{{ old('mother_job') }}" required>
                @error('mother_job')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
    </div>

    <!-- بيانات الطلاب -->
    <div class="card mb-4">
        <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
            <span>{{ __('messages.students') }}</span>
            <button type="button" class="btn btn-sm btn-success" onclick="addStudent()">
                <i class="fas fa-plus"></i> {{ __('messages.add_student') }}
            </button>
        </div>
        <div class="card-body">
            <p class="text-info mb-3">{{ __('messages.multiple_students_info') }}</p>
            <div id="studentsContainer">
                <!-- First student (always present) -->
                <div class="student-form border rounded p-3 mb-3" data-student-index="0">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">{{ __('messages.student_number') }} 1</h6>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label">{{ __('messages.student_name') }} <span class="text-danger">*</span></label>
                            <input type="text" name="students[0][name]" class="form-control {{ $hasError('students.0.name') }}"
                                value="{{ old('students.0.name') }}" required>
                            @error('students.0.name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">{{ __('messages.school') }} <span class="text-danger">*</span></label>
                            <select name="students[0][school]" class="form-select {{ $hasError('students.0.school') }}" onchange="toggleOtherSchoolStudent(this, 0)" required>
                                @foreach(__('schools') as $key => $value)
                                    <option value="{{ $key }}" {{ old('students.0.school') == $key ? 'selected' : '' }}>
                                        {{ $value }}
                                    </option>
                                @endforeach
                            </select>
                            @error('students.0.school')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-3 mb-3" id="otherSchoolStudentDiv0" style="display: {{ old('students.0.school') == 'other' ? 'block' : 'none' }};">
                            <label class="form-label">{{ __('messages.other_school') }} <span class="text-danger">*</span></label>
                            <input type="text" name="students[0][other_school]" class="form-control {{ $hasError('students.0.other_school') }}"
                                   value="{{ old('students.0.other_school') }}">
                            @error('students.0.other_school')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <label class="form-label">{{ __('messages.education_department') }} <span class="text-danger">*</span></label>
                            <select class="form-select {{ $hasError('students.0.education_department') }}" name="students[0][education_department]" required>
                                <option value="national" {{ old('students.0.education_department') == 'national' ? 'selected' : '' }}>{{ __('messages.national') }}</option>
                                <option value="international" {{ old('students.0.education_department') == 'international' ? 'selected' : '' }}>{{ __('messages.international') }}</option>
                                <option value="igsec" {{ old('students.0.education_department') == 'igsec' ? 'selected' : '' }}>{{ __('messages.igsec') }}</option>
                                <option value="french" {{ old('students.0.education_department') == 'french' ? 'selected' : '' }}>{{ __('messages.french') }}</option>
                                <option value="american" {{ old('students.0.education_department') == 'american' ? 'selected' : '' }}>{{ __('messages.american') }}</option>
                                <option value="dutch" {{ old('students.0.education_department') == 'dutch' ? 'selected' : '' }}>{{ __('messages.dutch') }}</option>
                            </select>
                            @error('students.0.education_department')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">{{ __('messages.education_stage') }} <span class="text-danger">*</span></label>
                            <select class="form-select {{ $hasError('students.0.education_stage') }}" name="students[0][education_stage]" required>
                                <option value="junior" {{ old('students.0.education_stage') == 'junior' ? 'selected' : '' }}>{{ __('messages.junior') }}</option>
                                <option value="kg" {{ old('students.0.education_stage') == 'kg' ? 'selected' : '' }}>{{ __('messages.kg') }}</option>
                                <option value="primary" {{ old('students.0.education_stage') == 'primary' ? 'selected' : '' }}>{{ __('messages.primary') }}</option>
                                <option value="preparatory" {{ old('students.0.education_stage') == 'preparatory' ? 'selected' : '' }}>{{ __('messages.preparatory') }}</option>
                                <option value="secondary" {{ old('students.0.education_stage') == 'secondary' ? 'selected' : '' }}>{{ __('messages.secondary') }}</option>
                            </select>
                            @error('students.0.education_stage')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">{{ __('messages.class_level') }} <span class="text-danger">*</span></label>
                            <input type="text" name="students[0][class_level]" class="form-control {{ $hasError('students.0.class_level') }}"
                                value="{{ old('students.0.class_level') }}" required>
                            @error('students.0.class_level')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">{{ __('messages.entry_time') }} <span class="text-danger">*</span></label>
                            <input type="time" name="students[0][entry_time]" class="form-control {{ $hasError('students.0.entry_time') }}"
                                value="{{ old('students.0.entry_time') }}" required>
                            @error('students.0.entry_time')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">{{ __('messages.exit_time') }} <span class="text-danger">*</span></label>
                            <input type="time" name="students[0][exit_time]" class="form-control {{ $hasError('students.0.exit_time') }}"
                                value="{{ old('students.0.exit_time') }}" required>
                            @error('students.0.exit_time')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بيانات التواصل -->
    <div class="card mb-4">
        <div class="card-header bg-dark text-white">{{ __('messages.contact_priority') }}</div>
        <div class="card-body row">
            <div class="col-md-4 mb-3">
                <label class="form-label">{{ __('messages.father_phone') }} <span class="text-danger">*</span></label>
                <input type="phone" name="father_phone" class="form-control {{ $hasError('father_phone') }}"
                    value="{{ old('father_phone') }}" required>
                @error('father_phone')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">{{ __('messages.mother_phone') }} <span class="text-danger">*</span></label>
                <input type="phone" name="mother_phone" class="form-control {{ $hasError('mother_phone') }}"
                    value="{{ old('mother_phone') }}" required>
                @error('mother_phone')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">{{ __('messages.home_phone') }}</label>
                <input type="phone" name="home_phone" class="form-control {{ $hasError('home_phone') }}"
                    value="{{ old('home_phone') }}">
                @error('home_phone')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label">{{ __('messages.extra_phone') }}</label>
                <input type="phone" name="extra_phone" class="form-control {{ $hasError('extra_phone') }}"
                    value="{{ old('extra_phone') }}">
                @error('extra_phone')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label">{{ __('messages.contact_priority') }} <span
                        class="text-danger">*</span></label>
                <select class="form-select {{ $hasError('contact_priority') }}" name="contact_priority" required>
                    <option value="father">{{ __('messages.contact_father') }}</option>
                    <option value="mother">{{ __('messages.contact_mother') }}</option>
                </select>
                @error('contact_priority')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
    </div>

    <!-- العنوان والمدرسة -->
    <div class="card mb-4">
        <div class="card-header bg-dark text-white">{{ __('messages.address') }}</div>
        <div class="card-body row">
            <div class="col-md-7 mb-3">
                <label class="form-label">{{ __('messages.address') }} <span class="text-danger">*</span></label>
                <input type="text" name="address" class="form-control {{ $hasError('address') }}"
                    value="{{ old('address') }}" required>
                @error('contact_priority')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-2 mb-3">
                <label class="form-label">{{ __('messages.area') }} <span
                        class="text-danger">*</span></label>
                <select class="form-select {{ $hasError('area') }}" name="area"
                    required>
                    @foreach(__('areas') as $key => $value)
                        <option value="{{ $key }}" {{ old('area') == $key ? 'selected' : '' }}>
                            {{ $value }}
                        </option>
                    @endforeach
                </select>
                @error('area')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">{{ __('messages.location') }}</label>
                <div class="input-group">
                    <input type="text" id="locationInput" name="location" class="form-control {{ $hasError('location') }}"
                        value="{{ old('location') }}">
                    <button type="button" class="btn btn-outline-primary" onclick="getLocation()">📍</button>
                </div>
                @error('location')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
    </div>



    <!-- السيارة والاشتراك -->
    <div class="card mb-4">
        <div class="card-header bg-dark text-white">{{ __('messages.car_type') }} <span class="text-danger">*</span>
        </div>
        <div class="card-body">
            @php
                $cars = [
                    ['value' => 'qasrawy', 'img' => 'qasrawy.png'],
                    ['value' => 'high_roof', 'img' => 'high_roof.png'],
                    ['value' => 'private7', 'img' => 'private7.png'],
                    ['value' => 'private_classic', 'img' => 'private_classic.png'],
                    ['value' => 'coaster', 'img' => 'coaster.png'],
                    ['value' => 'chevrolet', 'img' => 'chevrolet.png'],
                ];
            @endphp

            <div class="row">
                @foreach ($cars as $car)
                    <div class="col-md-4 mb-3 text-center">
                        <input type="radio" class="btn-check {{ $hasError('car_type') }}" name="car_type"
                            id="{{ $car['value'] }}" value="{{ $car['value'] }}" autocomplete="off"
                            {{ old('car_type') === $car['value'] ? 'checked' : '' }}>
                        <label class="btn btn-outline-secondary w-100" for="{{ $car['value'] }}">
                            <img src="{{ asset('images/cars/' . $car['img']) }}" alt="{{ $car['value'] }}"
                                class="img-fluid mb-2" style="max-height: 100px;">
                            <div>{{ __('messages.' . $car['value']) }}</div>
                        </label>
                        @error('car_type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                @endforeach
            </div>

            <div class="row mt-3">
                <div class="col-md-4 mb-3">
                    <label class="form-label">{{ __('messages.study_start_date') }} <span
                            class="text-danger">*</span></label>
                    <input type="date" name="study_start_date"
                        class="form-control {{ $hasError('study_start_date') }}"
                        value="{{ old('study_start_date') }}" required>
                    @error('study_start_date')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ __('messages.client_type') }} <span
                            class="text-danger">*</span></label>
                    <select class="form-select {{ $hasError('client_type') }}" name="client_type" required>
                        <option value="new">{{ __('messages.new_client') }}</option>
                        <option value="old">{{ __('messages.old_client') }}</option>
                    </select>
                    @error('client_type')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ __('messages.subscription_type') }} <span
                            class="text-danger">*</span></label>
                    <select class="form-select {{ $hasError('subscription_type') }}" name="subscription_type"
                        required>
                        <option value="one_time">{{ __('messages.subscription_one_time') }}</option>
                        <option value="monthly">{{ __('messages.subscription_monthly') }}</option>
                    </select>
                    @error('subscription_type')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>
    </div>

    <!-- تعليقات وزر -->
    <div class="mb-4">
        <label class="form-label">{{ __('messages.comments') }}</label>
        <textarea name="comments" class="form-control {{ $hasError('comments') }}" rows="3">{{ old('comments') }}</textarea>
        @error('comments')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="text-center mb-4">
        <button type="submit" class="btn btn-success px-5">{{ __('messages.submit') }}</button>
    </div>
</form>
