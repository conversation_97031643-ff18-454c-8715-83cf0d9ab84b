@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar-custom sidebar">
            <div class="position-sticky pt-3">
                <div class="text-center mb-4">
                    <img src="/images/logo.png" alt="Royal Transit Logo" style="max-width: 90px; height: auto; margin-bottom: 10px;">
                    <h5 class="mt-2">Royal Transit</h5>
                    <p class="text-muted small">Admin Dashboard</p>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    @can('read admin_clients')
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.clients.index') }}">
                            <i class="fas fa-users me-2"></i>Clients
                        </a>
                    </li>
                    @endcan
                    @can('read admin_routes')
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('admin.routes.index') }}">
                            <i class="fas fa-route me-2"></i>Routes
                        </a>
                    </li>
                    @endcan
                    @can('read admin_drivers')
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.drivers.index') }}">
                            <i class="fas fa-car me-2"></i>Drivers
                        </a>
                    </li>
                    @endcan
                    @can('read admin_financial_reports')
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.financial.index') }}">
                            <i class="fas fa-chart-bar me-2"></i>Financial
                        </a>
                    </li>
                    @endcan
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.subscriptions.index') }}">
                            <i class="fas fa-clipboard-list me-2"></i>Subscriptions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.payments.index') }}">
                            <i class="fas fa-credit-card me-2"></i>Payments
                        </a>
                    </li>
                </ul>
                
                <hr>
                
                <div class="dropdown">
                    <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" 
                       id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle me-2"></i>
                        <strong>{{ Auth::user()->name }}</strong>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark text-small shadow" aria-labelledby="dropdownUser1">
                        <li><a class="dropdown-item" href="{{ route('profile.show') }}">Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item">Sign out</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-route me-2"></i>Route Management
                </h1>
                @can('create admin_routes')
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ route('admin.routes.create') }}" class="btn btn-main">
                        <i class="fas fa-plus me-1"></i>Add New Route
                    </a>
                </div>
                @endcan
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Routes</h6>
                                    <h3 class="mb-0">{{ \App\Models\TransportRoute::count() }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-route fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Active Routes</h6>
                                    <h3 class="mb-0">{{ \App\Models\TransportRoute::where('status', 'active')->count() }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Capacity</h6>
                                    <h3 class="mb-0">{{ \App\Models\TransportRoute::sum('capacity') }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Current Students</h6>
                                    <h3 class="mb-0">{{ \App\Models\TransportRoute::sum('current_students') }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-graduation-cap fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <button class="btn btn-outline-primary" onclick="showCapacityReport()">
                                            <i class="fas fa-chart-pie me-1"></i>Capacity Report
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <button class="btn btn-outline-success" onclick="showDriverAssignments()">
                                            <i class="fas fa-user-tie me-1"></i>Driver Assignments
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <button class="btn btn-outline-warning" onclick="showScheduleOverview()">
                                            <i class="fas fa-clock me-1"></i>Schedule Overview
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <a href="#" class="btn btn-outline-info">
                                            <i class="fas fa-download me-1"></i>Export Routes
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- DataTable -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Routes List
                    </h5>
                </div>
                <div class="card-body">
                    {!! $dataTable->table(['class' => 'table table-striped table-hover']) !!}
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Modals -->
<!-- Capacity Report Modal -->
<div class="modal fade" id="capacityReportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-pie me-2"></i>Route Capacity Report
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="capacityReportContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
{!! $dataTable->scripts() !!}

<script>
function showCapacityReport() {
    $('#capacityReportModal').modal('show');
    // Load capacity report data via AJAX
    // This would be implemented with actual data loading
}

function showDriverAssignments() {
    // Show driver assignments overview
    alert('Driver assignments feature coming soon!');
}

function showScheduleOverview() {
    // Show schedule overview
    alert('Schedule overview feature coming soon!');
}


</script>
@endpush
@endsection

@push('styles')
<style>
    .sidebar-custom {
        background: linear-gradient(135deg, var(--main-blue) 0%, var(--main-gold) 100%);
        min-height: 100vh;
    }
    
    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        margin-bottom: 0.25rem;
    }
    
    .sidebar .nav-link:hover,
    .sidebar .nav-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .sidebar .nav-link i {
        width: 20px;
    }
    
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        background-color: #f8f9fa;
    }
    
    .btn-outline-primary:hover,
    .btn-outline-success:hover,
    .btn-outline-warning:hover,
    .btn-outline-info:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
@endpush
