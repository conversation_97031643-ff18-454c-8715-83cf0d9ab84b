<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StoreClientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->user()->can('create admin_clients');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            // Parent Information
            'father_job' => 'nullable|string|max:255',
            'mother_job' => 'nullable|string|max:255',
            'home_phone' => 'nullable|string|max:20',
            'mother_phone' => 'nullable|string|max:20',
            'father_phone' => 'nullable|string|max:20',
            'extra_phone' => 'nullable|string|max:20',
            'contact_priority' => 'required|in:father,mother',
            
            // Address Information
            'address' => 'required|string|max:500',
            'location' => 'nullable|string|max:255',
            'area' => 'required|string|max:255',
            
            // Subscription Information
            'study_start_date' => 'required|date|after_or_equal:today',
            'subscription_type' => 'required|in:one_time,monthly',
            'client_type' => 'required|in:new_client,old_client',
            'comments' => 'nullable|string|max:1000',
            
            // Students Information (array validation)
            'students' => 'required|array|min:1|max:10',
            'students.*.student_name' => 'required|string|max:255',
            'students.*.school' => 'required|string|max:255',
            'students.*.education_department' => 'required|in:national,international,igsec,french,american,dutch',
            'students.*.education_stage' => 'required|in:kg,primary,preparatory,secondary,middle,junior,senior',
            'students.*.class_level' => 'required|string|max:50',
            'students.*.entry_time' => 'required|date_format:H:i',
            'students.*.exit_time' => 'required|date_format:H:i|after:students.*.entry_time',
            'students.*.car_type' => 'required|in:qasrawy,high_roof,private7,private_classic,coaster,chevrolet',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'contact_priority.required' => __('messages.required_field'),
            'contact_priority.in' => __('Please select either Father or Mother for contact priority'),
            'address.required' => __('messages.required_field'),
            'address.max' => __('messages.max_length', ['max' => 500]),
            'area.required' => __('messages.required_field'),
            'study_start_date.required' => __('messages.required_field'),
            'study_start_date.date' => __('messages.invalid_date'),
            'study_start_date.after_or_equal' => __('Study start date must be today or in the future'),
            'subscription_type.required' => __('messages.required_field'),
            'subscription_type.in' => __('Please select a valid subscription type'),
            'client_type.required' => __('messages.required_field'),
            'client_type.in' => __('Please select a valid client type'),
            'comments.max' => __('messages.max_length', ['max' => 1000]),
            
            // Students validation messages
            'students.required' => __('At least one student is required'),
            'students.min' => __('At least one student is required'),
            'students.max' => __('Maximum 10 students allowed'),
            'students.*.student_name.required' => __('Student name is required'),
            'students.*.student_name.max' => __('messages.max_length', ['max' => 255]),
            'students.*.school.required' => __('School is required'),
            'students.*.school.max' => __('messages.max_length', ['max' => 255]),
            'students.*.education_department.required' => __('Education department is required'),
            'students.*.education_department.in' => __('Please select a valid education department'),
            'students.*.education_stage.required' => __('Education stage is required'),
            'students.*.education_stage.in' => __('Please select a valid education stage'),
            'students.*.class_level.required' => __('Class level is required'),
            'students.*.class_level.max' => __('messages.max_length', ['max' => 50]),
            'students.*.entry_time.required' => __('Entry time is required'),
            'students.*.entry_time.date_format' => __('Please enter a valid time format (HH:MM)'),
            'students.*.exit_time.required' => __('Exit time is required'),
            'students.*.exit_time.date_format' => __('Please enter a valid time format (HH:MM)'),
            'students.*.exit_time.after' => __('Exit time must be after entry time'),
            'students.*.car_type.required' => __('Car type is required'),
            'students.*.car_type.in' => __('Please select a valid car type'),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'father_job' => __('messages.father_job'),
            'mother_job' => __('messages.mother_job'),
            'home_phone' => __('messages.home_phone'),
            'mother_phone' => __('messages.mother_phone'),
            'father_phone' => __('messages.father_phone'),
            'extra_phone' => __('messages.extra_phone'),
            'contact_priority' => __('messages.contact_priority'),
            'address' => __('messages.address'),
            'location' => __('messages.location'),
            'area' => __('messages.area'),
            'study_start_date' => __('messages.study_start_date'),
            'subscription_type' => __('messages.subscription_type'),
            'client_type' => __('messages.client_type'),
            'comments' => __('messages.comments'),
            'students.*.student_name' => __('messages.student_name'),
            'students.*.school' => __('messages.school'),
            'students.*.education_department' => __('messages.education_department'),
            'students.*.education_stage' => __('messages.education_stage'),
            'students.*.class_level' => __('messages.class_level'),
            'students.*.entry_time' => __('messages.entry_time'),
            'students.*.exit_time' => __('messages.exit_time'),
            'students.*.car_type' => __('messages.car_type'),
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Clean phone numbers
        $phoneFields = ['home_phone', 'mother_phone', 'father_phone', 'extra_phone'];
        foreach ($phoneFields as $field) {
            if ($this->has($field)) {
                $this->merge([
                    $field => preg_replace('/[^0-9+]/', '', $this->input($field))
                ]);
            }
        }

        // Ensure students is an array
        if ($this->has('students') && !is_array($this->input('students'))) {
            $this->merge(['students' => []]);
        }
    }
}
