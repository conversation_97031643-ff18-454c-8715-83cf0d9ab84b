<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'name',
        'school',
        'other_school',
        'education_department',
        'education_stage',
        'class_level',
        'entry_time',
        'exit_time',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }
}
